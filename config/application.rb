require_relative "boot"

require "rails"

# Pick the frameworks you want:
require "active_model/railtie"
require "active_job/railtie"
require "active_record/railtie"
require "active_storage/engine"
require "action_controller/railtie"
require "action_mailer/railtie"
#require "action_mailbox/engine"
require "action_text/engine"
require "action_view/railtie"
require "action_cable/engine"
# require "rails/test_unit/railtie"

# Require the gems listed in Gemfile, including any gems
# you've limited to :test, :development, or :production.
Bundler.require(*Rails.groups)

module UnlistersApp
  class Application < Rails::Application
    # Initialize configuration defaults for originally generated Rails version.
    config.load_defaults 7.0

    # Configuration for the application, engines, and railties goes here.
    #
    # These settings can be overridden in specific environments using the files
    # in config/environments, which are processed later.
    #
    config.time_zone = "CET"
    # config.eager_load_paths << Rails.root.join("extras")
    
    # Add app/lib to autoload paths for custom error classes
    config.autoload_paths << Rails.root.join("app", "lib")

    # Don't generate system test files.
    config.generators.system_tests = nil

    # Default Rails way of disabling Active Storage routes
    config.active_storage.draw_routes = false
    
     # Enabling Rack::Attack
    config.middleware.use Rack::Attack

    # Configure I18n
    config.i18n.available_locales = [:en, :sk, :cs]
    config.i18n.default_locale = :sk
    config.i18n.fallbacks = true

    # Load translations from all subdirectories
    config.i18n.load_path += Dir[Rails.root.join('config', 'locales', '**', '*.{rb,yml}')]

    # Configure Active Job to use GoodJob adapter
    config.active_job.queue_adapter = :good_job
    
    # Configure Active Storage to use libvips for better performance
    config.active_storage.variant_processor = :vips
    
    # Security: Validate IP addresses in secure file tokens
    # Set to false in development if testing across different IPs
    config.validate_token_ip = Rails.env.production?
    
    # File upload configuration
    config.max_upload_size = 100.megabytes
    config.max_uploads_per_request = 10
    config.upload_rate_limit_per_user_per_hour = 100

  end
end
