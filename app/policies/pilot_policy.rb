# ABOUTME: Base policy for Pilot-tier only features using ActionPolicy pre_check
# ABOUTME: All policies inheriting from this will require pilot subscription access

class PilotPolicy < ApplicationPolicy
  # Use ActionPolicy's pre_check to enforce tier requirement
  # This runs before any specific rule in child policies
  pre_check :require_pilot_tier

  private

  def require_pilot_tier
    deny! unless pilot_feature_access?
  end
end