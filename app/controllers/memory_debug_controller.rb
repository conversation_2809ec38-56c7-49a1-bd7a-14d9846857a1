class MemoryDebugController < ApplicationController
  # Remove authentication and CSRF for easier debugging
  skip_before_action :authenticate_user!
  #skip_before_action :check_invite_only_registration
  skip_before_action :ensure_profile_complete
  skip_before_action :verify_authenticity_token
  
  def heap_dump
    timestamp = Time.current.to_i
    filename = Rails.root.join('tmp', "heap-#{timestamp}.json")
    
    GC.start # Force garbage collection before dump
    
    require 'objspace'
    File.open(filename, 'w') do |file|
      ObjectSpace.dump_all(output: file)
    end
    
    render json: { 
      message: "Heap dump created: #{filename}",
      timestamp: timestamp,
      filename: filename.to_s
    }
  end
  
  def memory_stats
    require 'objspace'
    
    GC.start # Force garbage collection first
    
    stats = {
      timestamp: Time.current.to_f,
      process_memory_mb: `ps -o rss= -p #{Process.pid}`.to_i / 1024.0,
      gc_stats: GC.stat,
      object_counts: ObjectSpace.count_objects,
      top_object_types: top_object_types_by_count(20),
      activerecord_stats: activerecord_connection_stats,
      suspect_objects: find_suspect_objects
    }
    
    render json: stats
  end
  
  def force_gc
    before_mb = `ps -o rss= -p #{Process.pid}`.to_i / 1024.0
    
    GC.start
    
    after_mb = `ps -o rss= -p #{Process.pid}`.to_i / 1024.0
    freed_mb = before_mb - after_mb
    
    render json: {
      message: "Garbage collection completed",
      memory_before_mb: before_mb.round(2),
      memory_after_mb: after_mb.round(2),
      freed_mb: freed_mb.round(2)
    }
  end
  
  def clear_query_cache
    ActiveRecord::Base.connection.clear_query_cache
    render json: { message: "ActiveRecord query cache cleared" }
  end
  
  # Test specific memory leak scenario
  def test_projects_leak
    before_stats = gather_memory_snapshot
    
    # Simulate the problematic index action
    begin
      user = User.first || User.create!(email: '<EMAIL>', password: 'password123')
      
      # Execute the suspected query
      10.times do
        projects = Project.full_list_for_user(user).includes(user: :user_profile).active.approved
        projects = projects.near('Prague', 20, units: :km) if defined?(Geocoder)
        projects.load # Force query execution
      end
      
      after_stats = gather_memory_snapshot
      
      render json: {
        message: "Projects leak test completed",
        before: before_stats,
        after: after_stats,
        memory_growth_mb: after_stats[:memory_mb] - before_stats[:memory_mb],
        object_growth: after_stats[:total_objects] - before_stats[:total_objects]
      }
    rescue => e
      render json: { error: "Test failed: #{e.message}" }
    end
  end

  private
  
  def gather_memory_snapshot
    GC.start
    {
      timestamp: Time.current.to_f,
      memory_mb: `ps -o rss= -p #{Process.pid}`.to_i / 1024.0,
      total_objects: ObjectSpace.count_objects[:TOTAL],
      gc_count: GC.count,
      suspect_objects: find_suspect_objects
    }
  end
  
  def activerecord_connection_stats
    {
      connection_pool_size: ActiveRecord::Base.connection_pool.size,
      active_connections: ActiveRecord::Base.connection_pool.connections.count,
      query_cache_enabled: ActiveRecord::Base.connection.query_cache_enabled,
      query_cache_size: ActiveRecord::Base.connection.query_cache.size
    }
  rescue => e
    { error: e.message }
  end
  
  def find_suspect_objects
    suspects = {}
    
    # Look for objects we suspect are leaking
    ObjectSpace.each_object do |obj|
      begin
        # Skip objects that don't have a proper class
        next unless obj.class && obj.class.name
        
        case obj.class.name
        when /Geocoder/
          suspects[:geocoder_objects] ||= 0
          suspects[:geocoder_objects] += 1
        when /ActiveRecord::Relation/
          suspects[:activerecord_relations] ||= 0
          suspects[:activerecord_relations] += 1
        when /ActionMailer/
          suspects[:action_mailer_objects] ||= 0
          suspects[:action_mailer_objects] += 1
        when /Project/
          suspects[:project_objects] ||= 0
          suspects[:project_objects] += 1
        when /NetworkConnection/
          suspects[:network_connection_objects] ||= 0
          suspects[:network_connection_objects] += 1
        end
      rescue NoMethodError
        # Skip objects like BasicObject that don't respond to class properly
        next
      end
    end
    
    suspects
  rescue => e
    { error: e.message }
  end
  
  def top_object_types_by_count(limit = 10)
    return {} unless defined?(ObjectSpace)
    
    counts = Hash.new(0)
    ObjectSpace.each_object do |obj|
      begin
        # Skip objects that don't have a proper class
        next unless obj.class && obj.class.name
        counts[obj.class.name] += 1
      rescue NoMethodError
        # Skip objects like BasicObject that don't respond to class properly
        next
      end
    end
    
    counts.sort_by { |_, count| -count }.first(limit).to_h
  rescue => e
    { error: e.message }
  end
end