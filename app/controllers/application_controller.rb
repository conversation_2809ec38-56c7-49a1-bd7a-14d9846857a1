class ApplicationController < ActionController::Base
  include Pagy::Backend
  
  layout :determine_layout
  protect_from_forgery with: :exception

  #before_action :http_basic_authenticate
  before_action :authenticate_user!
  # before_action :check_invite_only_registration  # Commented out to allow registration without invitation
  before_action :check_user_approval
  before_action :set_locale
  before_action :ensure_profile_complete
  #before_action :authorize!
  #after_action :verify_authorized

  skip_before_action :authenticate_user!, if: :devise_controller?
  skip_before_action :authenticate_user!, if: :welcome_controller?
  skip_before_action :authenticate_user!, only: [:redirect_to_locale, :require_authentication]
  # skip_before_action :check_invite_only_registration, only: [:redirect_to_locale, :require_authentication]  # Commented out since method is disabled
  skip_before_action :check_user_approval, if: :devise_controller?
  skip_before_action :check_user_approval, if: :welcome_controller?
  skip_before_action :check_user_approval, only: [:redirect_to_locale, :require_authentication]

  rescue_from ActionPolicy::Unauthorized do |exception|
    # Check if it's a tier restriction
    if exception.result&.reasons&.details&.include?(:require_pilot_tier)
      message = t('subscriptions.pilot_required',
                  default: 'This feature requires a Pilot subscription.')
    else
      message = t('common.unauthorized', 
                  default: 'You are not authorized to perform this action.')
    end
    
    respond_to do |format|
      format.html { redirect_back(fallback_location: root_path, alert: message) }
      format.json { render json: { error: message }, status: :forbidden }
    end
  end

  # This authenticates admin based on current_user's role
  def authenticate_admin_user_from_session!
    unless current_user && (current_user.super_admin? || current_user.admin?)
      flash[:alert] = "You are not authorized to access this section"
      redirect_to root_path
    end
  end

  # Return current_user for ActiveAdmin
  def current_admin_user_from_session
    current_user if current_user && (current_user.super_admin? || current_user.admin?)
  end

  # Handle authenticated routes accessed without authentication
  def require_authentication
    # Log the unauthorized access attempt
    Rails.logger.info "[AUTH_REQUIRED] Unauthenticated access attempt: #{request.path} from #{request.remote_ip} (User-Agent: #{request.user_agent}, Referer: #{request.referer})"
    
    # Store the requested path for redirect after login
    session[:user_return_to] = request.fullpath
    
    # Extract locale from path if present
    locale = request.path.match(%r{^/([a-z]{2})/})&.captures&.first || I18n.default_locale
    
    # Redirect to sign in page with appropriate locale
    redirect_to new_user_session_path(locale: locale), alert: t('devise.failure.unauthenticated')
  end

  # Handle requests without locale prefix
  def redirect_to_locale
    # Log the request for debugging
    Rails.logger.info "[LOCALE_REDIRECT] Request without locale: #{request.path} from #{request.remote_ip} (User-Agent: #{request.user_agent}, Referer: #{request.referer})"
    
    # Determine the appropriate locale
    locale = if user_signed_in? && current_user.user_profile&.default_language.present?
               current_user.user_profile.default_language
             else
               I18n.default_locale
             end
    
    # Build the new path with locale
    new_path = "/#{locale}#{request.path}"
    new_path += "?#{request.query_string}" if request.query_string.present?
    
    # Redirect to the localized version
    redirect_to new_path, status: :moved_permanently
  end

  private

  def set_locale
    # First check user preference - then params - then default
    I18n.locale = if user_signed_in? && current_user.user_profile&.default_language.present?
                    current_user.user_profile.default_language
                  elsif params[:locale].present?
                    params[:locale]
                  else
                    I18n.default_locale
                  end
  end
  
  # This method provides default options for URL generation.
  # By always including the current I18n.locale, it ensures that the locale
  # segment (e.g., /en/, /sk/) is consistently present in generated URLs,
  # even for the default locale. This helps maintain routing consistency.
  def default_url_options
    { locale: I18n.locale }
  end

  # def http_basic_authenticate
  #   authenticate_or_request_with_http_basic("Restricted Area") do |username, password|
  #     username == "tester" && password == "sigma"
  #   end
  # end

  # def check_invite_only_registration
  #   if ENV.fetch('INVITE_ONLY', 'false') == 'true' && 
  #      devise_controller? && 
  #      action_name == 'new' && 
  #      controller_name == 'registrations' && 
  #      !params[:invitation_token].present?
  #     redirect_to root_path, alert: 'Registration requires an invitation'
  #   end
  # end

  def check_user_approval
    # Skip if user is not signed in
    return unless user_signed_in?

    # Skip if user is not approved yet
    unless current_user.approved?
      # Allow access to user_profiles controller and logout
      return if controller_name == 'user_profiles' || (controller_name == 'sessions' && action_name == 'destroy')

      # Redirect unapproved users to their profile page (message shown on profile page)
      redirect_to edit_user_profile_path(current_user.user_profile)
      return
    end

    # Check if approved user needs to select subscription
    check_subscription_selection
  end

  def check_subscription_selection
    # Skip if user has already selected a subscription tier (not free by default)
    return if current_user.subscription_tier != 'free' || subscription_selection_completed?

    # Allow access to subscription-related controllers and user_profiles for subscription selection
    return if controller_name == 'user_profiles' && ['subscription_select', 'subscription_choose', 'validate_referral_code'].include?(action_name)
    return if controller_name == 'referral_codes'

    # Allow logout
    return if controller_name == 'sessions' && action_name == 'destroy'

    # Redirect to subscription selection for newly approved users
    redirect_to select_subscription_path
  end

  def subscription_selection_completed?
    # Check if user has explicitly chosen free tier or has made a subscription choice
    # For now, we'll assume users who have been around for a while have implicitly chosen free
    current_user.created_at < 1.day.ago || session[:subscription_selected]
  end

  def determine_layout
    if current_user.present?
      'application'
    else
      'welcome'
    end
  end

  def welcome_controller?
    self.class == WelcomeController
  end
  
  # def authorize!
  #   # Skip authorization for Devise controllers
  #   return if devise_controller?
    
  #   # For all other controllers, require authorization
  #   authorize! controller_name.classify.constantize
  # rescue NameError
  #   # If we can't find a policy, skip authorization
  #   skip_authorization
  # end
  
  def verify_authorized
    return if devise_controller?
    super
  end


  def ensure_profile_complete
    # Skip if no user is signed in, or if it's a Devise controller,
    # or if the user is trying to access the UserProfilesController to complete their profile.
    return if !user_signed_in? || devise_controller? || user_profiles_controller? || welcome_controller?

    profile = current_user.user_profile
    # Check if the profile exists and if essential fields are blank.
    # It's also important to check if current_user.user_profile itself is nil.
    if profile.nil? || profile.first_name.blank? || profile.last_name.blank? || profile.city.blank? || profile.country.blank?
      # Prevent redirection loop if already on the edit page, though user_profiles_controller? should catch this.
      # However, we also need to allow access to user_profile update/edit actions specifically.
      # The user_profiles_controller? check allows all actions within that controller.
      # If they are trying to sign out, allow that too.
      if controller_name == 'sessions' && action_name == 'destroy' # Allow sign_out
        return
      end

      # flash[:alert] = "Please complete your profile before continuing."
      # Assuming you have a route like edit_user_profile_path(@user_profile)
      # If user_profile is nil, we might need to redirect to a new_user_profile_path
      # or handle this scenario differently. For now, assuming profile exists but is incomplete.
      # Or, if profile is nil, current_user.create_user_profile might be an option before redirecting.
      # For now, let's assume user_profile object is always created upon user registration.
      redirect_to edit_user_profile_path(profile || current_user.build_user_profile)
    end
  end

  def user_profiles_controller?
    controller_name == 'user_profiles'
  end

end
