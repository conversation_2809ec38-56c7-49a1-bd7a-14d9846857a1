# ABOUTME: WantsController that follows exact same structure and patterns as ProjectsController
# ABOUTME: Implements index, show_my, CRUD operations with filtering, authorization and caching

class Wants<PERSON>ontroller < ApplicationController
  before_action :authenticate_user!
  before_action :set_want, except: [:show, :index, :new, :create, :show_my]
  
  # Caching setup identical to ProjectsController
  after_action :clear_geocoder_cache
  after_action :clear_template_cache_periodically

  # EXACT SAME pattern as projects#index
  def index
    # Authorize access to wants feature (pilot-tier only)
    authorize! Want, to: :index?

    setup_cached_translations

    @wants = Want.includes(user: :user_profile).active
                 .by_type(params[:want_type])
                 .by_category(params[:category])
                 .by_subcategory(params[:subcategory])
                 .search_summary(params[:search])
                 .by_location(params[:location])
                 .order(created_at: :desc)

    @pagy, @wants = pagy(@wants, limit: 10)
  end

  # EXACT SAME pattern as projects#show_my
  def show_my
    # Authorize access to wants feature (pilot-tier only)
    authorize! Want, to: :show_my?

    setup_cached_translations

    @wants = current_user.wants
                         .by_type(params[:want_type])
                         .by_category(params[:category])
                         .by_subcategory(params[:subcategory])
                         .search_summary(params[:search])
                         .by_location(params[:location])
                         .order(created_at: :desc)

    @pagy, @wants = pagy(@wants, limit: 10)
    render 'index'
  end

  def show
    begin
      @want = Want.find(params[:id])
      authorize! @want, to: :show?
    rescue ActiveRecord::RecordNotFound
      redirect_to wants_path, alert: t('wants.common.want_not_available', default: 'The requested want is not available.')
    end
  end

  def new
    @want = current_user.wants.build
    authorize! @want, to: :create?
    setup_cached_translations
  end

  def create
    @want = current_user.wants.build(want_params)
    authorize! @want, to: :create?
    
    if @want.save
      redirect_to @want, notice: t('wants.create.success', default: 'Want created successfully.')
    else
      setup_cached_translations
      render :new, status: :unprocessable_entity
    end
  end

  def edit
    authorize! @want
    setup_cached_translations
  end

  def update
    authorize! @want
    
    if @want.update(want_params)
      redirect_to @want, notice: t('wants.update.success', default: 'Want updated successfully.')
    else
      setup_cached_translations
      render :edit, status: :unprocessable_entity
    end
  end

  def destroy
    authorize! @want
    @want.destroy
    redirect_to show_my_wants_path, notice: t('wants.destroy.success', default: 'Want deleted successfully.'), status: :see_other
  end

  private

  def set_want
    @want = Want.find(params[:id])
  rescue ActiveRecord::RecordNotFound
    raise ActionController::RoutingError.new('Not Found')
  end

  def want_params
    params.require(:want).permit(:summary, :description, :want_type, :category, :subcategory, 
                                 :price_min, :price_max, :price_currency, 
                                 :place, :country, :country_code)
  end
  
  # EXACT SAME caching methods as ProjectsController
  def setup_cached_translations
    @cached_want_types = Project::PROJECT_TYPES
    @cached_categories = Project::CATEGORIES
    @cached_translations = Rails.cache.fetch("want_translations_#{I18n.locale}", expires_in: 1.hour) do
      {
        wantTypes: Hash[Want.want_types.keys.map { |t| [t, I18n.t("models.project.project_types.#{t}")] }],
        categories: Hash[Want.categories.keys.map { |c| [c, I18n.t("models.project.categories.#{c}")] }],
        subcategories: Hash[Want.subcategories.keys.map { |s| [s, I18n.t("models.project.subcategories.#{s}")] }]
      }
    end
    @cached_placeholders = Rails.cache.fetch("want_placeholders_#{I18n.locale}", expires_in: 1.hour) do
      {
        category: t("wants.index.filters.category_placeholder", default: "Category"),
        subcategory: t("wants.index.filters.subcategory_placeholder", default: "Subcategory")
      }
    end
  end

  def clear_template_cache_periodically
    # Clear template cache on 10% of requests to prevent memory buildup
    if Rails.env.production? && rand < 0.1
      begin
        # Clear ActionView template cache
        ActionView::LookupContext::DetailsKey.clear if defined?(ActionView::LookupContext::DetailsKey)
        
        # Clear I18n cache
        I18n.backend.reload! if I18n.backend.respond_to?(:reload!)
        
        Rails.logger.info "Template cache cleared to prevent memory leak"
      rescue => e
        Rails.logger.warn "Failed to clear template cache: #{e.message}"
      end
    end
  end

  def clear_geocoder_cache
    clear_geocoder_cache_safe
  end

  def clear_geocoder_cache_safe
    begin
      # Clear the geocoder cache using the official geocoder gem method
      if defined?(Geocoder)
        # Try the official cache expiration method first
        if Geocoder::Lookup.respond_to?(:get) && Geocoder.config[:lookup]
          lookup = Geocoder::Lookup.get(Geocoder.config[:lookup])
          if lookup && lookup.respond_to?(:cache) && lookup.cache
            lookup.cache.expire(:all)
            Rails.logger.debug("Geocoder cache expired using official method to prevent memory leak")
          end
        end
        
        # Also clear the configuration cache if it's a hash
        if Geocoder.config.cache.is_a?(Hash)
          Geocoder.config.cache.clear
          Rails.logger.debug("Geocoder configuration cache cleared to prevent memory leak")
        end
      end
      
      # Force garbage collection as additional safety measure
      GC.start
    rescue => e
      Rails.logger.warn("Failed to clear geocoder cache: #{e.message}")
      # Fall back to just forcing garbage collection
      GC.start
    end
  end
end
