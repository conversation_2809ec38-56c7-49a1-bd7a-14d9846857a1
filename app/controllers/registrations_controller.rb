require 'net/http'
require 'json'

class RegistrationsController < Devise::RegistrationsController
  def create
    # Verify reCAPTCHA if enabled
    if Rails.application.credentials.recaptcha&.dig(:secret_key).present? && !verify_recaptcha
      flash[:alert] = 'reCAPTCHA verification failed. Please try again.'
      redirect_to new_user_registration_path
      return
    end
    
    super
  end

  private

  def verify_recaptcha
    return true unless Rails.application.credentials.recaptcha&.dig(:secret_key).present?
    
    token = params[:recaptcha_token]
    return false if token.blank?

    # In development, allow bypass for domain issues
    if Rails.env.development? && token.present?
      return true
    end

    uri = URI('https://www.google.com/recaptcha/api/siteverify')
    response = Net::HTTP.post_form(uri, {
      'secret' => Rails.application.credentials.recaptcha[:secret_key],
      'response' => token,
      'remoteip' => request.remote_ip
    })

    result = JSON.parse(response.body)
    result['success'] && result['score'] && result['score'] >= 0.5
  rescue => e
    Rails.logger.error "reCAPTCHA verification error: #{e.message}"
    false
  end
end