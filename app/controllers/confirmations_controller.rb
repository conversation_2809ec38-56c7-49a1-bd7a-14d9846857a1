class ConfirmationsController < Devise::ConfirmationsController

  #Custom Devise confirmation controller to resend invitation to self
  def create
    @user = User.find_by_email(params[:user][:email])

    if @user&.invited_to_sign_up?
      @user.invite!
      redirect_to new_user_session_path,
        notice: 'New invitation sent to your email'
    else
      super
    end
  end

  # Override show action to handle automatic sign-in after confirmation
  # This fixes the issue where users were confirmed but not automatically signed in
  # due to conflicts between locale routing and <PERSON><PERSON>'s default session management
  def show
    self.resource = resource_class.confirm_by_token(params[:confirmation_token])
    yield resource if block_given?

    if resource.errors.empty?
      # User was successfully confirmed
      set_flash_message!(:notice, :confirmed)

      # Explicitly sign in the user after successful confirmation
      sign_in(resource_name, resource)

      # Redirect to the appropriate path with locale support
      respond_with_navigational(resource) { redirect_to after_confirmation_path_for(resource_name, resource) }
    else
      # Confirmation failed (invalid/expired token, etc.)
      respond_with_navigational(resource.errors, status: :unprocessable_entity) { render :new }
    end
  end

  protected

  # Fix for automatic sign-in after email confirmation
  # The issue was that <PERSON><PERSON>'s default redirect after confirmation doesn't respect
  # our locale routing setup. Users were being confirmed but redirected to login
  # instead of being signed in automatically as per standard Devise behavior.
  def after_confirmation_path_for(resource_name, resource)
    # Ensure locale is included in the redirect path for proper routing
    root_path(locale: I18n.locale)
  end
 end