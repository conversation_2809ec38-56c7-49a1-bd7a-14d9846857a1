class InvitationsController < ApplicationController
  before_action :authenticate_user!
  
  #InvitationsController would only handle email invitations using devise_invitable:
  
  def index
    # People you've invited
    @sent_invitations = User.where(invited_by_id: current_user.id)
  end

  #TODO: Add a check if the Users are already connected in both ways
  # And check if there is not a pending connecton request
  def create
    puts "params  ---------------------------------------------------------------------------- : #{params.inspect}"
    if User.exists?(email: params[:email])
      existing_user = User.find_by(email: params[:email])
      connection_request = ConnectionRequest.new(
        inviter: current_user,
        invitee: existing_user,
        message: "Let's connect!"
      )

      if connection_request.save
        redirect_to network_connections_path, notice: 'Invitation processed successfully!'
      else
        redirect_to invitations_path, alert: connection_request.errors.full_messages.to_sentence
      end

    else
      # The locale is passed from the form
      # It is used here to ensure the invitation email is sent in the selected language
      puts "params[:langlocale] ---------------------------------------------------------------------------- : #{params[:langlocale]}"
      I18n.with_locale(params[:langlocale]) do
        @user = User.invite!({ email: params[:email] }, current_user)
      end
      if @user.errors.empty?
        redirect_to network_connections_path, notice: t('invitations.create.success')
      else
        redirect_to invitations_path, alert: @user.errors.full_messages.to_sentence
      end
    end
  end

end