class Webhooks::ThumbnailsController < ApplicationController
  # Disable CSRF protection for webhook endpoint
  skip_before_action :verify_authenticity_token
  # Skip user authentication for webhook endpoint (external Lambda calls)
  skip_before_action :authenticate_user!
  # skip_before_action :check_invite_only_registration
  skip_before_action :set_locale
  skip_before_action :ensure_profile_complete
  
  before_action :verify_webhook_signature!
  before_action :check_request_size
  before_action :rate_limit_webhook

  def create
    # Rails.logger.info "=== WEBHOOK CREATE START ==="
    Rails.logger.info "Webhook received for original_blob_key: #{params[:original_blob_key]}"
    # Rails.logger.info "Full params: #{params.to_unsafe_h}"
    
    # Find the original blob by its S3 key
    original_blob = ActiveStorage::Blob.find_by(key: params.require(:original_blob_key))
    
    unless original_blob
      Rails.logger.warn "Webhook received for unknown original blob key: #{params[:original_blob_key]}"
      head :not_found
      return
    end

    # Find the parent record that has this blob attached
    parent_record = original_blob.attachments.first&.record
    
    unless parent_record
      Rails.logger.warn "Could not find parent record for blob key: #{params[:original_blob_key]}"
      head :unprocessable_entity
      return
    end

    # SECURITY: Check if thumbnail already exists (idempotency)
    # The parent record is a Project, and thumbnails are stored in pdf_thumbnails
    if parent_record.respond_to?(:pdf_thumbnails) && parent_record.pdf_thumbnails.any?
      # Check if we already have a thumbnail for this specific file
      file_attachment = original_blob.attachments.find { |a| a.record == parent_record }
      if file_attachment && parent_record.thumbnail_for_file(file_attachment)
        Rails.logger.info "Thumbnail already exists for #{parent_record.class.name} ##{parent_record.id}, file: #{original_blob.filename}"
        head :ok
        return
      end
    end

    # Create the thumbnail blob and attach it (idempotent)
    ActiveRecord::Base.transaction do
      thumbnail_params = params.require(:thumbnail).permit(:key, :filename, :content_type, :byte_size, :checksum)
      
      # LEGACY: Old Rails-specific filename generation pattern (kept for reference)
      # file_attachment = original_blob.attachments.find { |a| a.record == parent_record }
      # if file_attachment
      #   file_hash = parent_record.generate_secure_file_hash(file_attachment)
      #   thumbnail_filename = "thumb_#{file_hash[0..8]}_#{original_blob.filename.base}.png"
      #   thumbnail_params[:filename] = thumbnail_filename
      # end
      
      # LEGACY (v1): Use the filename provided by Lambda (S3 key + .png format)
      # Lambda already generates appropriate filenames for S3 storage
      # This allows Lambda to manage its own naming convention independently
      # thumbnail_blob = ActiveStorage::Blob.create!(thumbnail_params.merge(service_name: :amazon_thumbnails))
      # parent_record.pdf_thumbnails.attach(thumbnail_blob)
      
      # NEW (v2): UUID-based secure thumbnail handling with idempotency
      # SECURITY: Use UUID-based thumbnail filename (no user data exposure)
      # Lambda generates pure UUID keys like: uploads/.../thumbnails/550e8400-e29b-41d4-a716-************.png
      thumbnail_params[:filename] = "thumbnail.png"  # Generic name, key provides uniqueness
      
      # IDEMPOTENT: Find existing thumbnail or create new one
      thumbnail_blob = ActiveStorage::Blob.find_by(key: thumbnail_params[:key])
      
      if thumbnail_blob
        Rails.logger.info "Reusing existing thumbnail blob: #{thumbnail_params[:key]}"
      else
        # Create new thumbnail blob
        thumbnail_blob = ActiveStorage::Blob.create!(thumbnail_params.merge(service_name: :amazon_thumbnails))
        Rails.logger.info "Created new thumbnail blob: #{thumbnail_params[:key]}"
      end
      
      # IDEMPOTENT: Check if thumbnail is already attached to this parent record
      unless parent_record.pdf_thumbnails.include?(thumbnail_blob)
        parent_record.pdf_thumbnails.attach(thumbnail_blob)
        Rails.logger.info "Attached thumbnail to #{parent_record.class.name} ##{parent_record.id}"
      else
        Rails.logger.info "Thumbnail already attached to #{parent_record.class.name} ##{parent_record.id}"
      end
    end

    # Rails.logger.info "=== WEBHOOK CREATE SUCCESS ==="
    head :ok
    
  rescue ActionController::ParameterMissing => e
    Rails.logger.error "Invalid webhook payload: #{e.message}"
    render json: { error: e.message }, status: :bad_request
    
  rescue ActiveRecord::RecordInvalid => e
    Rails.logger.error "Failed to create thumbnail blob: #{e.record.errors.full_messages}"
    render json: { error: e.record.errors.full_messages }, status: :unprocessable_entity
  end

  private

  def check_request_size
    if request.content_length > 5.kilobytes
      Rails.logger.warn "Webhook request too large: #{request.content_length} bytes"
      head :payload_too_large
    end
  end

  def rate_limit_webhook
    # Simple rate limiting - uses rack-attack in production
    key = "webhook:#{request.remote_ip}"
    if Rails.cache.read(key).to_i >= 10 # Max 10 requests per minute
      Rails.logger.warn "Rate limit exceeded for webhook from #{request.remote_ip}"
      head :too_many_requests
      return
    end
    Rails.cache.write(key, Rails.cache.read(key).to_i + 1, expires_in: 1.minute)
  end

  def verify_webhook_signature!
    timestamp = request.headers['HTTP_X_SIGNATURE_TIMESTAMP']
    received_signature = request.headers['HTTP_X_SIGNATURE_HMAC_SHA256']
    
    # DEBUG: Log all webhook details
    # Rails.logger.info "=== WEBHOOK DEBUG ==="
    # Rails.logger.info "Timestamp header: #{timestamp}"
    # Rails.logger.info "Received signature: #{received_signature}"
    # Rails.logger.info "Request body: #{request.raw_post}"
    # Rails.logger.info "ALL headers: #{request.headers.to_h}"
    
    # Check if headers are present
    unless timestamp && received_signature
      Rails.logger.warn "Webhook missing signature headers"
      head :unauthorized
      return
    end

    # Check timestamp (prevent replay attacks)
    request_time = Time.at(timestamp.to_i)
    if request_time < 5.minutes.ago || request_time > 1.minute.from_now
      Rails.logger.warn "Webhook timestamp outside acceptable range: #{request_time}"
      head :unauthorized
      return
    end

    # Verify HMAC signature - use the exact body that was received
    request_body = request.env['RAW_POST_DATA'] || request.raw_post
    
    # CRITICAL: Force UTF-8 encoding to match Python/Lambda side
    request_body = request_body.force_encoding('UTF-8')
    timestamp_str = timestamp.to_s.force_encoding('UTF-8')
    string_to_sign = "#{timestamp_str}.#{request_body}".force_encoding('UTF-8')
    
    # DEBUG: Try manual HMAC calculation to isolate the issue
    secret = Rails.application.credentials.thumbnail_webhook_secret
    
    # Ensure secret is also UTF-8 encoded
    secret_utf8 = secret.force_encoding('UTF-8')
    
    expected_signature = OpenSSL::HMAC.hexdigest(
      'sha256',
      secret_utf8,
      string_to_sign
    )
    
    # ADDITIONAL DEBUG: Try with raw binary secret
    # expected_signature_binary = OpenSSL::HMAC.hexdigest(
    #   'sha256',
    #   secret.force_encoding('BINARY'),
    #   string_to_sign
    # )
    
    # Rails.logger.info "Secret encoding: #{secret.encoding}"
    # Rails.logger.info "Expected signature (UTF-8 secret): #{expected_signature}"
    # Rails.logger.info "Expected signature (BINARY secret): #{expected_signature_binary}"

    # Rails.logger.info "String to sign: #{string_to_sign}"
    # Rails.logger.info "String to sign (dumped): #{string_to_sign.dump}"
    # Rails.logger.info "Request body (dumped): #{request_body.dump}"
    # Rails.logger.info "Timestamp (dumped): #{timestamp_str.dump}"
    # Rails.logger.info "String to sign length: #{string_to_sign.length}"
    # Rails.logger.info "String to sign encoding: #{string_to_sign.encoding}"
    # Rails.logger.info "Rails secret (first 10 chars): #{Rails.application.credentials.thumbnail_webhook_secret[0..9]}..."
    # Rails.logger.info "Expected signature: #{expected_signature}"
    # Rails.logger.info "=== END WEBHOOK DEBUG ==="

    unless ActiveSupport::SecurityUtils.secure_compare(expected_signature, received_signature)
      Rails.logger.warn "Invalid webhook signature"
      head :unauthorized
    end
  end
end