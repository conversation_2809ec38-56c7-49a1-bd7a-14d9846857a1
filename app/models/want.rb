# ABOUTME: Want model that follows exact same patterns as Project model for consistency
# ABOUTME: Uses Project enums, validations, scopes and translation methods for data consistency

class Want < ApplicationRecord
  belongs_to :user
  
  # Use custom attribute type for decimal fields that supports Slovak comma separators
  attribute :price_min, :decimal_with_comma
  attribute :price_max, :decimal_with_comma
  
  # Reuse Project enums for data consistency
  enum want_type: Project.project_types
  enum category: Project.categories  
  enum subcategory: Project.subcategories
  
  
  # Validations (similar to Project)
  validates :summary, presence: true, length: { minimum: 10, maximum: 500 }
  validates :want_type, presence: true
  validates :category, presence: true
  validates :subcategory, presence: true
  validates :price_min, numericality: { greater_than_or_equal_to: 0, less_than: 10_000_000_000_000 }, allow_blank: true
  validates :price_max, numericality: { greater_than_or_equal_to: 0, less_than: 10_000_000_000_000 }, allow_blank: true
  validates :price_currency, inclusion: { in: ['EUR', 'USD'] }, allow_blank: true
  
  
  # Scopes for search and filtering (mirror Project scopes)
  scope :active, -> { where(created_at: 30.days.ago..) }
  scope :by_type, ->(type) { where(want_type: type) if type.present? }
  scope :by_category, ->(category) { where(category: category) if category.present? }
  scope :by_subcategory, ->(subcategory) { where(subcategory: subcategory) if subcategory.present? }
  scope :price_range, ->(min, max) { 
    where('price_min <= ? AND price_max >= ?', max, min) if min.present? && max.present? 
  }
  scope :by_location, ->(location) { where('place ILIKE ?', "%#{location}%") if location.present? }
  scope :search_summary, ->(query) { where('summary ILIKE ?', "%#{query}%") if query.present? }
  
  # Translation methods (following Project pattern)
  def translated_want_type
    I18n.t("models.project.project_types.#{want_type}")
  end

  def translated_category
    I18n.t("models.project.categories.#{category}")
  end
  
  def translated_subcategory
    I18n.t("models.project.subcategories.#{subcategory}")
  end
  
  # Class methods for translated options (mirror Project pattern)
  def self.translated_want_types
    want_types.keys.map { |t| [I18n.t("models.project.project_types.#{t}"), t] }
  end
  
  def self.translated_categories_for(want_type)
    return [] unless want_type.present? && Project::PROJECT_TYPES[want_type.to_sym]
    Project::PROJECT_TYPES[want_type.to_sym].map { |c| [I18n.t("models.project.categories.#{c}"), c] }
  end
  
  
  
end
