class Project < ApplicationRecord
  include SecureFileAccess
  
  attr_accessor :admin_approver 
  attr_accessor :is_admin_approval_action 
  
  geocoded_by :location
  
  belongs_to :user
  has_many :project_auths, dependent: :destroy
  has_many :shared_with_users, through: :project_auths, source: :user
  has_many :connection_requests, dependent: :destroy
  has_many_attached :private_files, service: :amazon_uploads
  has_many_attached :pdf_thumbnails, service: :amazon_thumbnails
  
  # Use custom attribute type for decimal fields that supports Slovak comma separators
  attribute :price_value, :decimal_with_comma
  attribute :commission, :decimal_with_comma
  
  PROJECT_TYPES = {
    real_estate: %w[commercial_property land homes offices],
    business: %w[business_acquisition private_equity franchise investment_network],
    intellectual_property: %w[patent trademark copyright technology],
    special: %w[art_work collectibles estate_collection],
    marketing_partnerships: %w[performance_marketing affiliate_programs channel_partners white_label_reseller strategic_sales_partners],
    talent: %w[talent_network],
    other: %w[uncategorized]
  }.freeze

  CATEGORIES = {
    # Real Estate Categories
    commercial_property: %w[industrial_building warehouse commercial_premises other_building],
    land: %w[developed buildable non_building],
    homes: %w[flat house],
    offices: %w[office],
    
    # Business Categories
    business_acquisition: %w[asset_purchase business_purchase],
    private_equity: %w[startup expansion_funding_round family_office],
    franchise: %w[unspecified],
    investment_network: %w[unspecified],
    
    # Intellectual Property Categories
    patent: %w[unspecified],
    trademark: %w[unspecified],
    copyright: %w[unspecified],
    technology: %w[unspecified],
    
    # Special Categories
    art_work: %w[unspecified],
    collectibles: %w[unspecified],
    estate_collection: %w[unspecified],
    
    # Marketing Partnerships Categories
    performance_marketing: %w[unspecified],
    affiliate_programs: %w[unspecified],
    channel_partners: %w[unspecified],
    white_label_reseller: %w[unspecified],
    strategic_sales_partners: %w[unspecified],
    
    # Talent Categories
    talent_network: %w[unspecified],
    
    # Other Category
    uncategorized: %w[unspecified]
  }.freeze

  enum project_type: {
    real_estate: 0,
    business: 1,
    intellectual_property: 2,
    special: 3,
    marketing_partnerships: 4,
    talent: 5,
    other: 6
  }

  enum category: {
    # Real Estate Categories
    commercial_property: 0,
    land: 1,
    homes: 2,
    offices: 3,
    
    # Business Categories
    business_acquisition: 4,
    private_equity: 5,
    franchise: 6,
    investment_network: 7,
    
    # Intellectual Property Categories
    patent: 8,
    trademark: 9,
    copyright: 10,
    technology: 11,
    
    # Special Categories
    art_work: 12,
    collectibles: 13,
    estate_collection: 14,
    
    # Marketing Partnerships Categories
    performance_marketing: 15,
    affiliate_programs: 16,
    channel_partners: 17,
    white_label_reseller: 18,
    strategic_sales_partners: 19,
    
    # Talent Categories
    talent_network: 20,
    
    # Other Category
    uncategorized: 21
  }

  enum subcategory: {
    # Commercial Property Subcategories
    industrial_building: 0,
    warehouse: 1,
    commercial_premises: 2,
    other_building: 3,
    
    # Land Subcategories
    developed: 4,
    buildable: 5,
    non_building: 6,
    
    # Homes Subcategories
    flat: 7,
    house: 8,
    
    # Offices Subcategories
    office: 9,
    
    # Business Acquisition Subcategories
    asset_purchase: 10,
    business_purchase: 11,
    
    # Private Equity Subcategories
    startup: 12,
    expansion_funding_round: 13,
    family_office: 14,
    
    # Generic placeholder
    unspecified: 15
  }


  # Conditional validations - only enforce for published projects
  validates :project_type, presence: true, if: :published?
  validates :category, presence: true, if: :published?
  validates :subcategory, presence: true, if: :published?
  validates :location, presence: true, if: :published?
  validates :price_currency, inclusion: { in: ['', 'EUR'], message: -> (object, data) { I18n.t('activerecord.errors.models.project.attributes.price_currency.invalid_currency', value: data[:value]) } }, allow_blank: true
  validates :commission_type, inclusion: { in: ['', 'EUR', '%'], message: -> (object, data) { I18n.t('activerecord.errors.models.project.attributes.commission_type.invalid_type', value: data[:value]) } }, allow_blank: true
  validates :price_value, numericality: { greater_than_or_equal_to: 0, less_than: 10_000_000_000_000 }, allow_blank: true
  validates :commission, numericality: { greater_than_or_equal_to: 0, less_than: 100_000_000 }, allow_blank: true
  validates :private_files, 
            content_type: [
              'application/pdf', 
              'application/msword', 
              'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 
              'image/png', 
              'image/jpeg', 
              'image/tiff'
            ], 
            size: { less_than: 50.megabytes }
  validate :summary_present, if: :published?
  validate :subcategory_matches_category, if: :published?
  validate :validate_sharing_options, if: :published_or_admin_approval?
  validate :validate_sharing_consistency, if: :published?
  validate :limit_summary_length
  validate :validate_approval_permission, if: :approved_changed?

  after_initialize :set_default_values, if: :new_record?
  after_validation :geocode, if: :should_geocode?
  before_save :set_default_summary_only
  before_save :ensure_sharing_consistency, if: :will_save_change_to_project_status?
  before_save :set_approval_status_based_on_changes, unless: :is_admin_approval_action
  # REMOVED: after_commit callback that caused race conditions with S3 uploads
  # Thumbnail generation now handled by Active Storage notifications

  scope :summary_visible_public, -> { where(public_visible: true).select(:id, :summary) }
  scope :summary_for_registred, -> { where(summary_only: true).select(:id, :summary, :location) }
  scope :full_access, -> { where(full_access: true) }
  scope :active, -> { where(project_status: true) }
  scope :approved, -> { where(approved: true) }
  scope :not_yet_published, -> { where(first_published_at: nil) }

  # Status-based scopes for admin dashboard
  scope :drafts, -> { where(project_status: false) }
  scope :pending_approval, -> { where(project_status: true, approved: false) }
  scope :published, -> { where(project_status: true, approved: true) }

  # Index visiblity of Project summary:
  # 1. semi_public - Project is visible to all registered users
  # 2. network - Project is visible to conneced users
  # public_visible - Project is visible to all users -- NOT used now as it will be standalone action
  # friends_only - Project is visible to friends only -- NOT used now as being simplified

  # One single query with auths
  # SECURITY NOTE: This query uses string interpolation but is SAFE because:
  # 1. user.id comes from authenticated session (current_user), NOT from user input
  # 2. It's an integer primary key from the database, not user-controllable
  # 3. connection.quote() properly escapes the value as an additional safety layer
  # 4. No user input path exists to inject SQL - the ID is from Rails' authenticated session
  # DO NOT refactor without understanding the complex LEFT JOIN OR conditions required here
  scope :full_list_for_user, ->(user) {
    joins(<<~SQL)
      LEFT JOIN network_connections ON 
        (network_connections.invitee_id = #{connection.quote(user.id)} AND network_connections.inviter_id = projects.user_id) OR
        (network_connections.inviter_id = #{connection.quote(user.id)} AND network_connections.invitee_id = projects.user_id)
      LEFT JOIN project_auths ON 
        project_auths.project_id = projects.id AND 
        project_auths.user_id = #{connection.quote(user.id)}
      LEFT JOIN users ON users.id = projects.user_id
    SQL
    .select(
      :id,
      :summary_only,
      :semi_public,
      :full_access,
      :network_only,
      :summary,
      :location,
      :category,
      :subcategory,
      :project_type,
      :updated_at,
      :user_id,
      :approved,
      'users.id as owner_id',
      'project_auths.access_level AS auth_level',
      'project_auths.id AS auth_id'
    )
    .where(
      "(projects.network_only = true AND network_connections.id IS NOT NULL) OR
       projects.semi_public = true OR
       projects.user_id = :user_id",
      user_id: user.id
    )
    .where("projects.project_status = true OR projects.user_id = :user_id", user_id: user.id)
    .order(updated_at: :desc)
  }

  # Access to the Project and its files is strictly limited to:
  # 1. owner
  # 2. to the Users with granted ProjectAuth level full_details
  def accessible_by?(user)
    return true if self.user == user
    return true if project_auths.find_by(user: user)&.approved_full?
    false
  end

  # def summary_or_full_must_be_set
  #   unless summary_only || full_access
  #     errors.add(:base, "Either Title Only or Full Project must be set")
  #   end
  # end

  def auth_exists_for?(user)
    project_auths.exists?(user_id: user.id)
  end

  def auth_level_for(user)
    project_auths.find_by(user_id: user.id)&.access_level
  end

  def pdf_files
    private_files.select { |file| file.content_type == 'application/pdf' }
  end
  
  def image_files
    private_files.select { |file| file.blob.variable? }
  end

  def thumbnail_for_file(file)
    content_type = file.content_type
    
    # Support both PDFs and images for pre-generated thumbnails
    return nil unless (content_type == 'application/pdf' || file.blob.variable?)
    
    file_hash = generate_secure_file_hash(file)
    
    # UPDATED: Support multiple thumbnail naming patterns
    # Try Rails pattern first (legacy thumbnails)
    thumbnail = pdf_thumbnails
      .joins(:blob)
      .where("active_storage_blobs.filename LIKE ?", "thumb_#{file_hash[0..8]}%")
      .first
    
    # If not found, try Lambda pattern - S3 key based
    if thumbnail.nil?
      # Lambda thumbnails use the original blob's S3 key with .png extension
      lambda_filename_pattern = file.blob.key.gsub(/\.[^.]+$/, '.png')
      thumbnail = pdf_thumbnails
        .joins(:blob)
        .where("active_storage_blobs.key = ?", lambda_filename_pattern)
        .first
    end
    
    # REMOVED: Flawed directory-wide fallback that caused security issue
    # where all files in same directory would get the same thumbnail
    # TODO: Implement explicit blob-to-thumbnail linking
    
    thumbnail
  end

  def needs_thumbnail_generation?
    # Check both PDF files AND image files for missing thumbnails
    all_thumbnailable_files = pdf_files + image_files
    all_thumbnailable_files.any? { |file| thumbnail_for_file(file).nil? }
  end

  def translated_project_type
    return nil if project_type.blank?
    I18n.t("models.project.project_types.#{project_type}")
  end

  def translated_category
    return nil if category.blank?
    I18n.t("models.project.categories.#{category}")
  end
  
  def translated_subcategory
    return nil if subcategory.blank?
    I18n.t("models.project.subcategories.#{subcategory}")
  end

  def self.translated_project_types
    project_types.keys.map { |c| [I18n.t("models.project.project_types.#{c}"), c] }
  end

  def self.translated_categories
    categories.keys.map { |c| [I18n.t("models.project.categories.#{c}"), c] }
  end
  
  def self.translated_subcategories_for(category)
    return [] unless category.present? && CATEGORIES.key?(category.to_sym)
    
    CATEGORIES[category.to_sym].map { |s| [I18n.t("models.project.subcategories.#{s}"), s] }
  end

  def self.translated_categories_for(project_type)
    return [] unless project_type.present? && PROJECT_TYPES.key?(project_type.to_sym)
    
    PROJECT_TYPES[project_type.to_sym].map { |c| [I18n.t("models.project.categories.#{c}", default: c.to_s.humanize), c] }
  end

  def user_has_access?(user)
    return false if user.nil?
    return true if user_id == user.id

    # Project must be approved for any non-owner access
    return false unless approved?

    return true if project_auths.exists?(user_id: user.id, access_level: 'full_details')

    # Check automatic access rules (same logic as policy)
    if full_access?
      return true if semi_public?
      return user_connected_to_owner?(user) if network_only?
    end

    false
  end

  # Returns the current status of the project for admin dashboard
  def status
    if project_status && approved?
      'published'
    elsif project_status && !approved?
      'pending'
    else
      'draft'
    end
  end

  # Returns a human-readable status
  def status_label
    case status
    when 'published'
      'Published'
    when 'pending'
      'Pending'
    when 'draft'
      'Draft'
    end
  end

  # Helper methods for draft/published status
  def published?
    project_status == true
  end

  def draft?
    project_status == false
  end


  private

  # REMOVED: generate_pdf_thumbnails method - now handled by Active Storage notifications

  def private_files_attached?
    private_files.attached?
  end

  def should_geocode?
    published? && location.present? && (location_changed? || latitude.blank? || longitude.blank?)
  end


  def summary_present
    errors.add(:summary, :blank) if summary.blank?
  end

  def limit_summary_length
    return unless summary
    return if summary.length <= 123
    errors.add(:summary, :too_long)
  end

  def subcategory_matches_category
    return unless category && subcategory
    return if CATEGORIES[category.to_sym].include?(subcategory)
    errors.add(:subcategory, I18n.t('activerecord.errors.models.project.attributes.subcategory.invalid'))
  end

  def validate_sharing_options
    unless summary_only? || full_access?
      errors.add(:base, I18n.t('activerecord.errors.models.project.attributes.base.sharing_options_required'))
    end

    unless network_only? || semi_public?
      errors.add(:base, I18n.t('activerecord.errors.models.project.attributes.base.visibility_required'))
    end
  end

  def validate_sharing_consistency
    # Exactly one visibility option must be true
    visibility_count = [network_only?, semi_public?].count(true)
    if visibility_count != 1
      errors.add(:base, I18n.t('activerecord.errors.models.project.attributes.base.sharing_consistency'))
    end

    # Exactly one detail level option must be true
    detail_count = [summary_only?, full_access?].count(true)
    if detail_count != 1
      errors.add(:base, I18n.t('activerecord.errors.models.project.attributes.base.detail_consistency'))
    end
  end

  def set_default_values
    # Set defaults to match UI expectations: data-audience="all" and data-access="full"
    self.summary_only = false if summary_only.nil?  # Default: NOT summary only (full access)
    self.network_only = false if network_only.nil?  # Default: NOT network only (everyone)
    self.full_access = true if full_access.nil?      # Default: Full access
    self.semi_public = true if semi_public.nil?      # Default: Everyone can see
    
    # Set enum fields to nil for new records to enable proper dirty tracking
    # Override Rails automatic enum defaults for draft projects
    self.project_type = nil if project_type.present? && new_record?
    self.category = nil if category.present? && new_record?
    self.subcategory = nil if subcategory.present? && new_record?
  end

  def set_default_summary_only
    if !summary_only && !full_access
      self.summary_only = true
    end
  end

  def validate_approval_permission
    # This validation relies on the :admin_approver virtual attribute
    # being set in the controller before save.
    return if admin_approver && (admin_approver.super_admin? || admin_approver.admin?)
    errors.add(:approved, I18n.t('activerecord.errors.models.project.attributes.approved.permission_denied'))
  end

  def set_approval_status_based_on_changes
    # If it's a new record or the summary has been modified,
    # set approved to false, requiring admin review.
    # Approved projects gets set false - but the notification is only sent to admin when published. 
    if (new_record? || summary_changed?)
      self.approved = false
    end
  end

  def user_connected_to_owner?(user)
    NetworkConnection.where(
      '(inviter_id = ? AND invitee_id = ?) OR (inviter_id = ? AND invitee_id = ?)',
      user.id, user_id, user_id, user.id
    ).exists?
  end

  def published_or_admin_approval?
    published? || is_admin_approval_action
  end

  def ensure_sharing_consistency
    # Ensure proper sharing defaults when publishing
    # This prevents validation failures due to form submission issues
    
    # Fix visibility options: ensure exactly one is true
    if !(network_only? || semi_public?)
      # Default to semi_public (everyone) if neither is set
      self.semi_public = true
      self.network_only = false
    elsif network_only? && semi_public?
      # If both somehow got set, prioritize semi_public
      self.network_only = false
    end
    
    # Fix access level options: ensure exactly one is true  
    if !(summary_only? || full_access?)
      # Default to full_access if neither is set
      self.full_access = true
      self.summary_only = false
    elsif summary_only? && full_access?
      # If both somehow got set, prioritize full_access
      self.summary_only = false
    end
  end

end
