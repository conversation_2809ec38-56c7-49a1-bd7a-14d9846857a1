# ABOUTME: ReferralCode model for subscription tier discounts with usage tracking
# ABOUTME: Includes validation, status management, and secure redemption logic with race condition protection
class ReferralCode < ApplicationRecord
  belongs_to :created_by, class_name: 'User'

  enum status: { active: 0, expired: 1, used_up: 2, disabled: 3 }
  enum tier_upgrade_to: { premium: 1, pilot: 2 }

  validates :code, presence: true, uniqueness: { case_sensitive: false }, length: { maximum: 12 }
  validates :max_uses, presence: true, numericality: { greater_than: 0 }
  validates :duration_months, presence: true, numericality: { greater_than: 0 }
  validates :discount_percentage, presence: true, numericality: { greater_than_or_equal_to: 0, less_than_or_equal_to: 100 }

  # Normalize code to uppercase for case insensitive handling
  before_save :normalize_code
  
  scope :available, -> { where(status: :active).where('expires_at > ? OR expires_at IS NULL', Time.current) }
  
  # Simplified redemption with race condition protection
  def redeem!(user)
    ReferralCode.transaction do
      lock! # Prevent race conditions

      raise Errors::CodeExpiredError if expires_at&.past?
      raise Errors::CodeLimitReachedError if current_uses >= max_uses
      raise Errors::CodeNotActiveError unless active?
      raise Errors::UserAlreadyPremiumError if user.active_subscription?

      # Apply tier upgrade only if discount is 100% (free tier)
      if discount_percentage == 100.0
        expiry_date = duration_months.months.from_now
        user.update!(
          subscription_tier: tier_upgrade_to,
          subscription_expires_at: tier_upgrade_to == 'pilot' ? nil : expiry_date
        )
      end
      # For partial discounts (< 100%), the discount is applied during payment processing
      # This redemption just marks the code as used and tracks usage

      # Update usage tracking
      increment!(:current_uses)
      update!(status: :used_up) if current_uses >= max_uses
    end
    true
  end

  # Check if this code provides a free tier upgrade (100% discount)
  def free_tier_upgrade?
    discount_percentage == 100.0
  end

  private

  def normalize_code
    self.code = code&.strip&.upcase
  end
end
