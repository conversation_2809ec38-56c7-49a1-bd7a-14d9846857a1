@import "variables";

// ABOUTME: Minimal admin-specific layout styles only - uses global styles for everything else
// ABOUTME: Flex-table layout for admin dashboard and basic admin container styling

// Admin container - simple wrapper
.admin-container {
  margin: 20px;
  max-width: 100%;
}

// Admin page header
.admin-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;

  h1 {
    margin: 0;
    color: $secondary-color;
  }
}

// Flex-table for admin dashboard - the only unique admin layout
.admin-flex-table {
  display: flex;
  flex-direction: column;
  border: 1px solid #e5e7eb;
  width: 100%;
  border-radius: 8px;
  overflow: hidden;
  background: white;
}

.admin-flex-table-header,
.admin-flex-table-row {
  display: flex;
  border-bottom: 1px solid #e5e7eb;
  align-items: stretch;
}

.admin-flex-table-header {
  background-color: #f8f9fa;
  font-weight: bold;
}

.admin-flex-table-cell {
  padding: 12px 10px;
  text-align: left;
  overflow: hidden;
  text-overflow: ellipsis;
  border-right: 1px solid #e5e7eb;
  display: flex;
  align-items: center;

  &:last-child {
    border-right: none;
  }

  // Column widths for project admin table
  &.admin-col-id { flex: 0 0 60px; }
  &.admin-col-email { flex: 0 0 280px; }
  &.admin-col-summary { flex: 1; }
  &.admin-col-date { flex: 0 0 150px; }
  &.admin-col-status { flex: 0 0 120px; text-align: center; justify-content: center; }
  &.admin-col-actions { flex: 0 0 320px; justify-content: center; text-align: center; }

  // Column widths for user management table
  &.admin-col-user-id { flex: 0 0 60px; }
  &.admin-col-profile-basic { flex: 0 0 250px; }
  &.admin-col-profile-contact { flex: 0 0 200px; }
  &.admin-col-tier { flex: 0 0 100px; text-align: center; justify-content: center; }
  &.admin-col-approval { flex: 0 0 120px; text-align: center; justify-content: center; }

  // Column widths for referral codes table
  &.admin-col-code { min-width: 120px; }
  &.admin-col-tier { min-width: 100px; }
  &.admin-col-duration { min-width: 80px; }
  &.admin-col-usage { min-width: 80px; }
  &.admin-col-expires { min-width: 120px; }
  &.admin-col-description { min-width: 200px; flex: 1; }
}

.admin-flex-table-row:nth-child(even) {
  background-color: #f9fafb;
}

.admin-flex-table-row:hover {
  background-color: #f3f4f6;
}

// Simple details layout for show pages
.admin-details-section {
  background: white;
  padding: 20px 0;

  .admin-detail-row {
    display: flex;
    padding: 10px 0;
    border-bottom: 1px solid #f0f0f0;

    &:last-child {
      border-bottom: none;
    }

    .admin-detail-label {
      font-weight: bold;
      width: 200px;
      color: #666;
    }

    .admin-detail-value {
      flex: 1;
    }
  }
}

// Profile details styling for user management table
.profile-details {
  font-size: 0.875rem;
  line-height: 1.4;
}

.profile-item {
  margin-bottom: 4px;
  display: flex;
  align-items: flex-start;

  &:last-child {
    margin-bottom: 0;
  }

  strong {
    font-weight: 600;
  }

  small {
    font-size: 0.8rem;
    color: #666;
    line-height: 1.3;
  }
}

// Muted text-link for less important actions
.text-link-muted {
  font-size: $base-font-size;
  color: #666;
  text-decoration: none;
  background: none;
  border: none;
  padding: 0;
  cursor: pointer;

  &:hover {
    color: $link-color;
    text-decoration: underline;
  }
}

// NEW MODERN ADMIN STYLES - based on design files

// Modern admin container with improved background  
.modern-admin-container {
  min-height: 100vh;
  background-color: #f7fafc;
  padding: 2rem;
}

// Modern admin header
.modern-admin-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;

  h1 {
    font-size: 1.875rem;
    font-weight: 700;
    color: #111827;
    margin: 0;
  }

  p {
    color: #6b7280;
    margin-top: 0.25rem;
    margin-bottom: 0;
  }

  .button {
    background-color: #374151;
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    border: none;
    text-decoration: none;
    transition: background-color 0.2s;

    &:hover {
      background-color: #4b5563;
    }
  }
}

// Modern card container
.modern-admin-card {
  background: white;
  padding: 1.5rem;
  border-radius: 1rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

// Modern filter buttons
.modern-filter-buttons {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #e5e7eb;

  .filter-button {
    padding: 0.5rem 1rem;
    border-radius: 9999px;
    font-size: 0.875rem;
    font-weight: 500;
    text-decoration: none;
    border: none;
    cursor: pointer;
    transition: all 0.2s;
    display: inline-flex;
    align-items: center;

    &.active {
      background-color: #3b82f6;
      color: white;

      .count {
        color: #bfdbfe;
        margin-left: 0.25rem;
      }
    }

    &:not(.active) {
      background-color: #f3f4f6;
      color: #374151;

      &:hover {
        background-color: #e5e7eb;
      }

      .count {
        color: #9ca3af;
        margin-left: 0.25rem;
      }
    }
  }
}

// Card grid layout
.admin-card-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1.5rem;
}

// Individual user card - no hover effects or shadows
.admin-user-card {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 0.75rem;
  padding: 1.25rem;
}

// Individual project card - no avatars/icons
.admin-project-card {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 0.75rem;
  padding: 1.25rem;
}

// User card header
.admin-user-card-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: 1rem;

  .user-info {
    display: flex;
    align-items: center;
    gap: 1rem;

    .user-avatar {
      width: 3rem;
      height: 3rem;
      border-radius: 50%;
      background-color: #e5e7eb;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: 600;
      color: #6b7280;
      font-size: 0.875rem;
      flex-shrink: 0;
    }

    .user-details {
      h3 {
        font-weight: 700;
        color: #111827;
        margin: 0;
        font-size: 1rem;
      }

      p {
        color: #6b7280;
        font-size: 0.875rem;
        margin: 0.25rem 0 0 0;
      }
    }
  }
}

// Status badges
.status-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: capitalize;
  display: inline-block;

  &.status-approved {
    background-color: #e6f7f2;
    color: #0d9488;
  }

  &.status-pending {
    background-color: #fffbeb;
    color: #d97706;
  }

  &.status-declined {
    background-color: #fee2e2;
    color: #dc2626;
  }
}

// User card content
.admin-user-card-content {
  margin-bottom: 1.25rem;

  p {
    font-size: 0.875rem;
    margin: 0;

    &.user-bio {
      color: #374151;
    }

    &.user-contact {
      color: #9ca3af;
      margin-top: 0.25rem;
    }
  }
}

// User card footer
.admin-user-card-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;

  .tier-badge {
    padding: 0.25rem 0.75rem;
    font-size: 0.75rem;
    color: #374151;
    background-color: #f3f4f6;
    border-radius: 9999px;
    font-weight: 500;
  }

  .card-actions {
    display: flex;
    align-items: center;
    gap: 0.5rem;

    .action-button {
      padding: 0.5rem;
      border-radius: 50%;
      border: none;
      cursor: pointer;
      background: none;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 1.25rem;

      &.decline {
        color: #ef4444;
      }

      &.approve {
        color: #10b981;
      }

      &.more {
        color: #6b7280;
      }
    }
  }
}

// Project card header
.admin-project-card-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: 1rem;

  .project-info {
    .project-details {
      h3 {
        font-weight: 700;
        color: #111827;
        margin: 0;
        font-size: 1rem;
      }

      p {
        color: #6b7280;
        font-size: 0.875rem;
        margin: 0.25rem 0 0 0;
      }
    }
  }
}

// Project card content
.admin-project-card-content {
  margin-bottom: 1.25rem;

  .project-summary {
    font-size: 0.875rem;
    color: #374151;
    margin: 0 0 0.5rem 0;
    line-height: 1.4;
  }

  .project-meta {
    font-size: 0.75rem;
    color: #9ca3af;
    margin: 0;
  }
}

// Project card footer
.admin-project-card-footer {
  .project-actions {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    flex-wrap: wrap;

    .action-button {
      padding: 0.375rem 0.75rem;
      border-radius: 0.375rem;
      border: none;
      cursor: pointer;
      background: none;
      font-size: 0.875rem;
      font-weight: 500;

      &.decline {
        color: #ef4444;
        border: 1px solid #ef4444;
      }

      &.approve {
        color: #10b981;
        border: 1px solid #10b981;
      }

      &.more {
        color: #6b7280;
        border: 1px solid #6b7280;
      }
    }
  }
}

// Dropdown menu - click-based only
.dropdown {
  position: relative;
  display: inline-block;

  .dropdown-menu {
    display: none;
    position: absolute;
    right: 0;
    top: 100%;
    margin-top: 0.5rem;
    width: 10rem;
    background: white;
    border-radius: 0.5rem;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    z-index: 10;

    a, form, input[type="submit"] {
      display: block;
      padding: 0.5rem 1rem;
      font-size: 0.875rem;
      text-decoration: none;
      color: #374151;
      width: 100%;
      text-align: left;
      border: none;
      background: none;
      cursor: pointer;

      &.danger {
        color: #dc2626;
      }
    }
  }

  &.active .dropdown-menu {
    display: block;
  }
}

// Heroicons styling for buttons
.hero-icon {
  width: 1.25rem;
  height: 1.25rem;
  display: inline-block;
}

// Modern Admin Table Styles - Matching new_admin_design_list.html template
.modern-admin-container {
  min-height: 100vh;
  background-color: #f7fafc;
}

.modern-admin-wrapper {
  padding: 2rem;
}

.modern-admin-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  
  .modern-admin-title {
    font-size: 1.875rem;
    font-weight: 700;
    color: #111827;
    margin: 0;
  }
  
  .modern-admin-subtitle {
    color: #6b7280;
    margin-top: 0.25rem;
    font-size: 0.875rem;
  }
}

.modern-admin-card {
  background: white;
  padding: 1.5rem;
  border-radius: 1rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

// Filter Pills Section
.modern-filter-section {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #e5e7eb;
  
  .filter-pill {
    padding: 0.5rem 1rem;
    border-radius: 9999px;
    font-size: 0.875rem;
    font-weight: 500;
    text-decoration: none;
    transition: background-color 0.2s;
    background-color: #f3f4f6;
    color: #374151;
    
    &:hover {
      background-color: #e5e7eb;
    }
    
    &.filter-pill-active {
      background-color: #3b82f6;
      color: white;
    }
  }
  
  .filter-count {
    margin-left: 0.25rem;
    color: #9ca3af;
    
    &.filter-count-active {
      color: #bfdbfe;
    }
  }
}

// Modern Table Styles
.modern-table-wrapper {
  overflow-x: auto;
  width: 100%;
}

.modern-admin-table {
  width: 100%;
  
  .modern-table-header-row {
    background-color: #f9fafb;
    text-align: left;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    color: #6b7280;
  }
  
  .modern-table-header {
    padding: 0.75rem 1.5rem;
    
    &.text-right {
      text-align: right;
    }
  }
  
  .modern-table-body {
    border-top: 1px solid #e5e7eb;
  }
  
  .modern-table-row {
    border-bottom: 1px solid #e5e7eb;
    transition: background-color 0.15s;
    
    &:hover {
      background-color: #f9fafb;
    }
  }
  
  .modern-table-cell {
    padding: 1rem 1.5rem;
    
    .table-cell-title {
      font-weight: 600;
      color: #111827;
      font-size: 0.875rem;
    }
    
    .table-cell-subtitle {
      font-size: 0.875rem;
      color: #6b7280;
      margin-top: 0.125rem;
    }
    
    .table-cell-detail {
      font-size: 0.875rem;
      color: #374151;
    }
  }
}

// Table Actions
.table-actions {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 0.25rem;
}

// Action Icon Buttons
.action-icon-btn {
  padding: 0.5rem;
  border-radius: 9999px;
  border: none;
  background: none;
  cursor: pointer;
  transition: all 0.2s;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  
  &.action-icon-red {
    color: #ef4444;
    
    &:hover {
      background-color: #fee2e2;
    }
  }
  
  &.action-icon-green {
    color: #10b981;
    
    &:hover {
      background-color: #d1fae5;
    }
  }
  
  &.action-icon-gray {
    color: #6b7280;
    
    &:hover {
      background-color: #e5e7eb;
    }
  }
}

.icon-small {
  width: 1.25rem;
  height: 1.25rem;
}

// Status Badges (keeping existing style names for compatibility)
.status-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: capitalize;
  display: inline-block;
  
  &.status-approved {
    background-color: #e6f7f2;
    color: #0d9488;
  }
  
  &.status-pending {
    background-color: #fffbeb;
    color: #d97706;
  }
  
  &.status-declined {
    background-color: #fee2e2;
    color: #dc2626;
  }
}

// Dropdown Menu (updated to match template)
.dropdown {
  position: relative;
  display: inline-block;
  
  .dropdown-menu {
    display: none;
    position: absolute;
    right: 0;
    top: 100%;
    margin-top: 0.5rem;
    width: 10rem;
    background: white;
    border-radius: 0.5rem;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    z-index: 10;
    overflow: hidden;
  }
  
  &.active .dropdown-menu {
    display: block;
  }
  
  .dropdown-link {
    display: block;
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    color: #374151;
    text-decoration: none;
    background: none;
    border: none;
    width: 100%;
    text-align: left;
    cursor: pointer;
    
    &:hover {
      background-color: #f3f4f6;
    }
    
    &.dropdown-link-danger {
      color: #dc2626;
      
      &:hover {
        background-color: #fee2e2;
      }
    }
  }
}

.no-data-message {
  text-align: center;
  color: #6b7280;
  margin: 2rem 0;
}

// Mobile responsive
@media (max-width: $breakpoint-mobile) {
  .modern-admin-wrapper {
    padding: 1rem;
  }
  
  .modern-admin-header {
    .modern-admin-title {
      font-size: 1.5rem;
    }
  }
  
  .modern-filter-section {
    flex-wrap: wrap;
    
    .filter-pill {
      font-size: 0.75rem;
      padding: 0.375rem 0.75rem;
    }
  }
  
  .modern-admin-table {
    .modern-table-header {
      padding: 0.5rem 0.75rem;
      font-size: 0.625rem;
    }
    
    .modern-table-cell {
      padding: 0.75rem;
      font-size: 0.75rem;
      
      .table-cell-title,
      .table-cell-subtitle,
      .table-cell-detail {
        font-size: 0.75rem;
      }
    }
  }
  
  .action-icon-btn {
    padding: 0.375rem;
  }
  
  .icon-small {
    width: 1rem;
    height: 1rem;
  }

  .admin-card-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .modern-filter-buttons {
    flex-wrap: wrap;
  }

  .admin-container {
    margin: 10px;
  }

  .admin-flex-table-header,
  .admin-flex-table-row {
    flex-direction: column;

    .admin-flex-table-cell {
      border-right: none;
      border-bottom: 1px solid #e5e7eb;

      &:last-child {
        border-bottom: none;
      }
    }
  }
}

// User Deletion Modal - Clean and readable
.delete-modal {
  display: none;
  position: fixed;
  z-index: 1000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
}

.delete-modal-content {
  background-color: white;
  margin: 8% auto;
  padding: 0;
  border-radius: 12px;
  width: 90%;
  max-width: 500px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

.delete-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px 32px;
  border-bottom: 1px solid #e5e7eb;

  h2 {
    margin: 0;
    font-size: 24px;
    font-weight: 600;
    color: #111827;
  }
}

.delete-modal-close {
  background: none;
  border: none;
  font-size: 32px;
  color: #9ca3af;
  cursor: pointer;
  padding: 0;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover {
    color: #374151;
  }
}

.delete-warning {
  padding: 24px 32px;
  background-color: #fef2f2;
  border-left: 4px solid #ef4444;
  margin: 0;

  h3 {
    margin: 0 0 16px 0;
    font-size: 18px;
    font-weight: 600;
    color: #dc2626;
  }

  p {
    margin: 0 0 12px 0;
    font-size: 16px;
    color: #7f1d1d;
    line-height: 1.5;
  }

  ul {
    margin: 0;
    padding-left: 20px;
    
    li {
      font-size: 16px;
      color: #7f1d1d;
      margin-bottom: 4px;
      line-height: 1.4;
    }
  }
}

.delete-confirmation {
  padding: 24px 32px;

  p {
    margin: 0 0 16px 0;
    font-size: 16px;
    color: #374151;
    line-height: 1.5;

    strong {
      color: #111827;
      font-weight: 600;
    }
  }

  .confirmation-text {
    background-color: #f3f4f6;
    padding: 12px 16px;
    border-radius: 6px;
    font-family: inherit;
    font-size: 16px;
    font-weight: 600;
    color: #111827;
    border: 2px solid #e5e7eb;
    margin-bottom: 20px;
    word-break: break-all;
  }

  input[type="text"] {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #d1d5db;
    border-radius: 6px;
    font-size: 16px;
    font-family: inherit;
    margin-bottom: 24px;
    box-sizing: border-box;

    &:focus {
      outline: none;
      border-color: #3b82f6;
      box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }
  }
}

.delete-modal-actions {
  display: flex;
  gap: 12px;

  .btn-cancel,
  .btn-delete {
    flex: 1;
    padding: 12px 24px;
    border: none;
    border-radius: 6px;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
    font-family: inherit;
  }

  .btn-cancel {
    background-color: #f3f4f6;
    color: #374151;

    &:hover {
      background-color: #e5e7eb;
    }
  }

  .btn-delete {
    background-color: #dc2626;
    color: white;

    &:hover:not(:disabled) {
      background-color: #b91c1c;
    }

    &:disabled {
      background-color: #9ca3af;
      cursor: not-allowed;
    }
  }
}

@media (max-width: 640px) {
  .delete-modal-content {
    margin: 4% auto;
    width: 95%;
  }

  .delete-modal-header,
  .delete-warning,
  .delete-confirmation {
    padding: 20px;
  }

  .delete-modal-header h2 {
    font-size: 20px;
  }

  .delete-warning {
    h3 {
      font-size: 16px;
    }

    p, li {
      font-size: 14px;
    }
  }

  .delete-confirmation {
    p, input[type="text"], .confirmation-text {
      font-size: 14px;
    }
  }

  .btn-cancel,
  .btn-delete {
    font-size: 14px;
    padding: 10px 20px;
  }
}