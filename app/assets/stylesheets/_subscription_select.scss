// ABOUTME: Subscription selection page styles with modern card layout design
// ABOUTME: Includes pricing tiers, referral code input, and responsive mobile-first approach

.subscription-page {
  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem 1rem;
  }

  .page-header {
    text-align: center;
    margin-bottom: 3rem;

    h1 {
      font-size: 2.5rem;
      font-weight: 700;
      color: $secondary-color;
      margin-bottom: 0.5rem;
    }

    .subtitle {
      font-size: 1.125rem;
      color: #666;
      margin-bottom: 0;
    }
  }

  // Referral code section
  .referral-section {
    max-width: 500px;
    margin: 0 auto 3rem;
    text-align: center;

    .section-title {
      font-size: 1rem;
      color: #666;
      margin-bottom: 1rem;
    }

    .referral-input-group {
      display: flex;
      gap: 0;
      border-radius: 8px;
      overflow: hidden;
      border: 1px solid #e0e0e0;

      .referral-input {
        flex: 1;
        border: none;
        background: white;
        padding: 12px 16px;
        font-size: 16px;
        text-transform: uppercase;
        letter-spacing: 1px;

        &:focus {
          outline: none;
          box-shadow: 0 0 0 3px rgba($primary-color, 0.1);
        }
      }

      .apply-button {
        background: $primary-color;
        color: white;
        border: none;
        padding: 12px 24px;
        font-weight: 600;
        cursor: pointer;
        font-size: 16px;
        transition: background-color 0.2s ease;

        &:hover {
          background: darken($primary-color, 10%);
        }

        &:disabled {
          opacity: 0.6;
          cursor: not-allowed;
        }
      }
    }

    .referral-feedback {
      margin-top: 1rem;
      padding: 0.75rem 1rem;
      border-radius: 6px;
      font-size: 0.9rem;
      font-weight: 500;

      &.success {
        background-color: #d1fae5;
        color: $green;
        border: 1px solid lighten($green, 30%);
      }

      &.error {
        background-color: #fee2e2;
        color: $attention-color;
        border: 1px solid lighten($attention-color, 30%);
      }
    }
  }

  // Pricing tiers
  .pricing-tiers {
    display: flex;
    gap: 2rem;
    justify-content: center;
    align-items: stretch;
    margin-bottom: 3rem;

    @media (max-width: $breakpoint-mobile) {
      flex-direction: column;
      gap: 1.5rem;
    }
  }

  .pricing-tier {
    flex: 1;
    max-width: 400px;
    background: white;
    border: 2px solid #e5e5e5;
    border-radius: 12px;
    padding: 0;
    display: flex;
    flex-direction: column;
    position: relative;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    }

    &.current-tier {
      border-color: $secondary-color;
      box-shadow: 0 4px 20px rgba($secondary-color, 0.15);
    }

    &.premium-tier {
      border-color: $primary-color;
      box-shadow: 0 4px 20px rgba($primary-color, 0.15);

      .tier-header {
        background: linear-gradient(135deg, #f0fdfd 0%, #e0f9f9 100%);
      }
    }

    .recommended-badge {
      position: absolute;
      top: -12px;
      left: 50%;
      transform: translateX(-50%);
      background: $primary-color;
      color: white;
      padding: 0.5rem 1.5rem;
      border-radius: 20px;
      font-size: 0.8rem;
      font-weight: 700;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }

    .tier-header {
      padding: 2rem 2rem 1rem;
      text-align: center;
      border-bottom: 1px solid #f0f0f0;

      h2 {
        font-size: 1.875rem;
        font-weight: 600;
        color: $secondary-color;
        margin-bottom: 0.5rem;
      }

      .tier-description {
        font-size: 0.9rem;
        color: #666;
        margin-bottom: 1.5rem;
      }

      .tier-price {
        .price-amount {
          font-size: 3rem;
          font-weight: 700;
          color: $secondary-color;
          line-height: 1;
        }

        .price-period {
          font-size: 1.125rem;
          color: #666;
          margin-left: 0.25rem;
        }

        .original-price {
          font-size: 0.9rem;
          color: #999;
          text-decoration: line-through;
          margin-top: 0.5rem;
        }
      }
    }

    .tier-features {
      padding: 1.5rem 2rem;
      flex: 1;

      .features-list {
        list-style: none;
        padding: 0;
        margin: 0;
        
        li {
          display: flex;
          align-items: center;
          padding: 0.75rem 0;
          font-size: 1rem;
          border-bottom: 1px solid #f5f5f5;

          &:last-child {
            border-bottom: none;
          }

          .check-icon {
            width: 20px;
            height: 20px;
            background: $green;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 0.75rem;
            flex-shrink: 0;

            &::after {
              content: '✓';
              color: white;
              font-weight: bold;
              font-size: 12px;
            }
          }

          &.excluded {
            color: #999;

            .check-icon {
              background: #e5e5e5;

              &::after {
                content: '✗';
                color: #999;
              }
            }
          }
        }
      }
    }

    .tier-action {
      padding: 2rem;
      border-top: 1px solid #f0f0f0;

      .tier-button {
        width: 100%;
        padding: 1rem 2rem;
        border: none;
        border-radius: 8px;
        font-size: 1rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.2s ease;
        text-decoration: none;
        display: inline-block;
        text-align: center;

        &.button-primary {
          background: $primary-color;
          color: white;

          &:hover {
            background: darken($primary-color, 10%);
            transform: translateY(-1px);
          }

          &:visited {
            color: white;
          }
        }

        &.button-secondary {
          background: $secondary-color;
          color: white;

          &:hover {
            background: lighten($secondary-color, 10%);
          }

          &:visited {
            color: white;
          }

          &:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;

            &:hover {
              background: $secondary-color;
              transform: none;
            }
          }
        }
      }
    }
  }

  // Info section
  .info-section {
    max-width: 800px;
    margin: 0 auto;
    background: #f8f9fa;
    border-radius: 12px;
    padding: 2rem;

    .section-title {
      font-size: 1.5rem;
      font-weight: 600;
      color: $secondary-color;
      margin-bottom: 1.5rem;
      text-align: center;
    }

    .info-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 1rem;

      .info-item {
        display: flex;
        align-items: flex-start;
        gap: 0.75rem;
        padding: 0.5rem 0;

        .info-icon {
          width: 20px;
          height: 20px;
          background: $primary-color;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          flex-shrink: 0;
          margin-top: 0.125rem;

          &::after {
            content: '✓';
            color: white;
            font-weight: bold;
            font-size: 12px;
          }
        }

        .info-text {
          font-size: 0.9rem;
          color: #666;
          line-height: 1.4;
        }
      }
    }
  }
}

// Loading animation for apply button
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  display: inline-block;
  margin-right: 0.5rem;
}

// Mobile responsive adjustments
@media (max-width: $breakpoint-mobile) {
  .subscription-page {
    .container {
      padding: 1rem;
    }

    .page-header {
      margin-bottom: 2rem;

      h1 {
        font-size: 2rem;
      }

      .subtitle {
        font-size: 1rem;
      }
    }

    .referral-section {
      margin-bottom: 2rem;

      .referral-input-group {
        flex-direction: column;
        border-radius: 8px;

        .referral-input {
          border-bottom: 1px solid #e0e0e0;
          border-radius: 8px 8px 0 0;
        }

        .apply-button {
          border-radius: 0 0 8px 8px;
        }
      }
    }

    .pricing-tier {
      .tier-header {
        padding: 1.5rem 1.5rem 1rem;

        h2 {
          font-size: 1.5rem;
        }

        .tier-price .price-amount {
          font-size: 2.5rem;
        }
      }

      .tier-features {
        padding: 1.5rem;
      }

      .tier-action {
        padding: 1.5rem;
      }
    }

    .info-section {
      padding: 1.5rem;
      margin: 1.5rem 0 0;

      .info-grid {
        grid-template-columns: 1fr;
      }
    }
  }
}