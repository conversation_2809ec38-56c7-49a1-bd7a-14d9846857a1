<!-- ABOUTME: Projects admin dashboard with modern table layout and styled action buttons -->
<!-- ABOUTME: Uses Heroicons for icons and matches the new_admin_design_list.html template styling -->

<div class="modern-admin-container">
  <div class="modern-admin-wrapper">
    <header class="modern-admin-header">
      <div>
        <h1 class="modern-admin-title">Projects Dashboard</h1>
        <p class="modern-admin-subtitle">Manage project approvals and status.</p>
      </div>
    </header>

    <div class="modern-admin-card">
      <!-- Filter Buttons -->
      <div class="modern-filter-section">
        <%= link_to user_admin_dashboard_path, class: "filter-pill #{'filter-pill-active' if @status_filter.blank?}" do %>
          All <span class="filter-count <%= @status_filter.blank? ? 'filter-count-active' : '' %>"><%= @status_counts[:total] %></span>
        <% end %>
        <%= link_to user_admin_dashboard_path(status: 'draft'), class: "filter-pill #{'filter-pill-active' if @status_filter == 'draft'}" do %>
          Drafts <span class="filter-count <%= @status_filter == 'draft' ? 'filter-count-active' : '' %>"><%= @status_counts[:draft] %></span>
        <% end %>
        <%= link_to user_admin_dashboard_path(status: 'pending'), class: "filter-pill #{'filter-pill-active' if @status_filter == 'pending'}" do %>
          Pending <span class="filter-count <%= @status_filter == 'pending' ? 'filter-count-active' : '' %>"><%= @status_counts[:pending] %></span>
        <% end %>
        <%= link_to user_admin_dashboard_path(status: 'published'), class: "filter-pill #{'filter-pill-active' if @status_filter == 'published'}" do %>
          Published <span class="filter-count <%= @status_filter == 'published' ? 'filter-count-active' : '' %>"><%= @status_counts[:published] %></span>
        <% end %>
      </div>

      <% if @projects && @projects.any? %>
        <div class="modern-table-wrapper">
          <table class="modern-admin-table">
            <thead>
              <tr class="modern-table-header-row">
                <th class="modern-table-header">Project</th>
                <th class="modern-table-header">Status</th>
                <th class="modern-table-header">User</th>
                <th class="modern-table-header">Details</th>
                <th class="modern-table-header text-right">Actions</th>
              </tr>
            </thead>
            <tbody class="modern-table-body">
              <% @projects.each do |project| %>
                <tr class="modern-table-row">
                  <td class="modern-table-cell">
                    <div>
                      <div class="table-cell-title">Project #<%= project.id %></div>
                      <div class="table-cell-subtitle"><%= project.title.presence || "Untitled" %></div>
                    </div>
                  </td>
                  <td class="modern-table-cell">
                    <span class="status-badge <%= project.status == 'published' ? 'status-approved' : project.status == 'pending' ? 'status-pending' : 'status-declined' %>">
                      <%= project.status_label %>
                    </span>
                  </td>
                  <td class="modern-table-cell">
                    <div>
                      <div class="table-cell-title"><%= project.user&.email || "N/A" %></div>
                      <div class="table-cell-subtitle"><%= project.updated_at.strftime("%b %d, %Y") %></div>
                    </div>
                  </td>
                  <td class="modern-table-cell">
                    <div class="table-cell-detail"><%= project.summary.presence || "No summary" %></div>
                  </td>
                  <td class="modern-table-cell">
                    <div class="table-actions">
                      <%= form_with(model: project, url: update_approval_project_path(project), method: :patch, local: true, html: { style: 'display: contents;' }) do |form| %>
                        <% if project.status != 'published' %>
                          <%= form.button name: "project[approved]", value: "false", type: :submit, class: "action-icon-btn action-icon-red", title: "Reject Project" do %>
                            <svg class="icon-small" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                          <% end %>
                          <%= form.button name: "project[approved]", value: "true", type: :submit, class: "action-icon-btn action-icon-green", title: "Approve Project" do %>
                            <svg class="icon-small" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                          <% end %>
                        <% else %>
                          <%= form.button name: "project[approved]", value: "false", type: :submit, class: "action-icon-btn action-icon-red", title: "Reject Project" do %>
                            <svg class="icon-small" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                          <% end %>
                        <% end %>
                      <% end %>
                      <div class="dropdown">
                        <button class="action-icon-btn action-icon-gray" type="button">
                          <svg class="icon-small" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z"></path>
                          </svg>
                        </button>
                        <div class="dropdown-menu">
                          <%= form_with(model: project, url: admin_destroy_project_path(project), method: :delete, local: true, data: { confirm: "Are you sure you want to delete this project?" }) do |form| %>
                            <%= form.button "Delete Project", type: :submit, class: "dropdown-link dropdown-link-danger" %>
                          <% end %>
                        </div>
                      </div>
                    </div>
                  </td>
                </tr>
              <% end %>
            </tbody>
          </table>
        </div>
      <% else %>
        <p class="no-data-message">No projects to display.</p>
      <% end %>
    </div>
  </div>
</div>