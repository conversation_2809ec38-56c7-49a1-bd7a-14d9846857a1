<% content_for :title, t('subscriptions.select.title') %>

<div class="subscription-page">
  <div class="container">
    <!-- Page Header -->
    <div class="page-header">
      <h1><%= t('subscriptions.select.title') %></h1>
      <p class="subtitle"><%= t('subscriptions.select.subtitle') %></p>
    </div>

    <!-- Referral Code Section -->
    <div class="referral-section">
      <p class="section-title"><%= t('subscriptions.select.referral_code.title') %></p>
      <div class="referral-input-group">
        <input type="text" id="referral-code-input" class="referral-input"
               placeholder="<%= t('subscriptions.select.referral_code.placeholder') %>">
        <button type="button" id="apply-code-btn" class="apply-button">
          <%= t('subscriptions.select.referral_code.apply') %>
        </button>
      </div>
      <div id="referral-feedback" class="referral-feedback" style="display: none;"></div>
    </div>

    <!-- Pricing Tiers -->
    <div class="pricing-tiers">
      <!-- Free Tier -->
      <div class="pricing-tier <%= 'current-tier' if current_user.tier_free? %>">
        <div class="tier-header">
          <h2><%= t('subscriptions.select.tiers.free.name') %></h2>
          <p class="tier-description">Perfect for getting started and exploring.</p>
          <div class="tier-price">
            <span class="price-amount"><%= t('subscriptions.select.tiers.free.price') %></span>
            <span class="price-period"><%= t('subscriptions.select.tiers.free.period') %></span>
          </div>
        </div>
        <div class="tier-features">
          <ul class="features-list">
            <% t('subscriptions.select.tiers.free.features').each_with_index do |feature, index| %>
              <li class="<%= index >= 3 ? 'excluded' : '' %>">
                <span class="check-icon"></span>
                <%= feature %>
              </li>
            <% end %>
          </ul>
        </div>
        <div class="tier-action">
          <% if current_user.tier_free? %>
            <button class="tier-button button-secondary" disabled><%= t('subscriptions.select.tiers.free.current') %></button>
          <% else %>
            <%= form_with url: choose_subscription_path, method: :post, local: true do |form| %>
              <%= form.hidden_field :tier, value: 'free' %>
              <%= form.submit t('subscriptions.select.tiers.free.choose'), class: "tier-button button-secondary" %>
            <% end %>
          <% end %>
        </div>
      </div>

      <!-- Premium Tier -->
      <div class="pricing-tier premium-tier <%= 'current-tier' if current_user.tier_premium? %>">
        <% unless current_user.tier_premium? %>
          <div class="recommended-badge"><%= t('subscriptions.select.tiers.premium.recommended') %></div>
        <% end %>
        <div class="tier-header">
          <h2><%= t('subscriptions.select.tiers.premium.name') %></h2>
          <p class="tier-description">For professionals who need more power.</p>
          <div id="premium-pricing" class="tier-price">
            <span class="price-amount"><%= t('subscriptions.select.tiers.premium.price') %></span>
            <span class="price-period"><%= t('subscriptions.select.tiers.premium.period') %></span>
          </div>
        </div>
        <div class="tier-features">
          <ul class="features-list">
            <% t('subscriptions.select.tiers.premium.features').each do |feature| %>
              <li>
                <span class="check-icon"></span>
                <%= feature %>
              </li>
            <% end %>
          </ul>
        </div>
        <div class="tier-action">
          <% if current_user.tier_premium? %>
            <button class="tier-button button-secondary" disabled><%= t('subscriptions.select.tiers.premium.current') %></button>
          <% else %>
            <button type="button" id="choose-premium-btn" class="tier-button button-primary">
              <%= t('subscriptions.select.tiers.premium.choose') %>
            </button>
          <% end %>
        </div>
      </div>
    </div>

    <!-- Additional Information -->
    <div class="info-section">
      <h3 class="section-title"><%= t('subscriptions.select.info.title') %></h3>
      <div class="info-grid">
        <% t('subscriptions.select.info.features').each do |feature| %>
          <div class="info-item">
            <span class="info-icon"></span>
            <span class="info-text"><%= feature %></span>
          </div>
        <% end %>
      </div>
    </div>
  </div>
</div>

<!-- Hidden form for premium selection -->
<%= form_with url: choose_subscription_path, method: :post, local: true, id: "premium-form", class: "hidden" do |form| %>
  <%= form.hidden_field :tier, value: 'premium' %>
  <%= form.hidden_field :referral_code, id: "premium-referral-code" %>
<% end %>

<script>
let appliedReferralCode = null;
const originalPremiumPrice = '<%= t('subscriptions.select.tiers.premium.price') %>';

// Translation strings for JavaScript
const translations = {
  enterCode: '<%= t('subscriptions.select.referral_code.enter_code') %>',
  applying: '<%= t('subscriptions.select.referral_code.applying') %>',
  apply: '<%= t('subscriptions.select.referral_code.apply') %>',
  error: '<%= t('subscriptions.select.referral_code.error') %>'
};

document.getElementById('apply-code-btn').addEventListener('click', function() {
  const code = document.getElementById('referral-code-input').value.trim().toUpperCase();

  if (!code) {
    showFeedback(translations.enterCode, 'error');
    return;
  }

  // Show loading state
  this.textContent = translations.applying;
  this.disabled = true;

  // AJAX call to validate referral code
  fetch('<%= validate_referral_code_path %>', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
    },
    body: JSON.stringify({ code: code })
  })
  .then(response => response.json())
  .then(data => {
    if (data.success) {
      appliedReferralCode = code;
      updatePremiumPricing(data.discount);
      showFeedback(data.message, 'success');
    } else {
      showFeedback(data.message, 'error');
    }
  })
  .catch(error => {
    showFeedback(translations.error, 'error');
  })
  .finally(() => {
    // Reset button state
    document.getElementById('apply-code-btn').textContent = translations.apply;
    document.getElementById('apply-code-btn').disabled = false;
  });
});

function showFeedback(message, type) {
  const feedback = document.getElementById('referral-feedback');
  feedback.className = `referral-feedback ${type}`;
  feedback.textContent = message;
  feedback.style.display = 'block';
}

function updatePremiumPricing(discount) {
  const pricingDiv = document.getElementById('premium-pricing');
  const period = '<%= t('subscriptions.select.tiers.premium.period') %>';

  if (discount.final_price === 0) {
    // Free upgrade
    pricingDiv.innerHTML = `
      <span class="price-amount">FREE</span>
      <span class="price-period">${period}</span>
      <div class="original-price">${originalPremiumPrice} - ${discount.description}</div>
    `;
  } else {
    // Discounted price
    pricingDiv.innerHTML = `
      <span class="price-amount">€${discount.final_price}</span>
      <span class="price-period">${period}</span>
      <div class="original-price">${originalPremiumPrice} - ${discount.description}</div>
    `;
  }
}

document.getElementById('choose-premium-btn').addEventListener('click', function() {
  // Set referral code in hidden form if applied
  if (appliedReferralCode) {
    document.getElementById('premium-referral-code').value = appliedReferralCode;
  }

  // Submit the premium form (will be handled by subscription team's payment flow)
  document.getElementById('premium-form').submit();
});

// Allow Enter key to apply referral code
document.getElementById('referral-code-input').addEventListener('keypress', function(e) {
  if (e.key === 'Enter') {
    document.getElementById('apply-code-btn').click();
  }
});
</script>

