<% content_for :title, t('subscriptions.select.title') %>

<div class="container">
  <div class="flex flex-column g1 align-start">
    <h1><%= t('subscriptions.select.title') %></h1>
    <p><%= t('subscriptions.select.subtitle') %></p>

    <!-- Referral Code Input -->
    <div class="form-card">
      <h3><%= t('subscriptions.select.referral_code.title') %></h3>
      <div class="flex g1 align-center">
        <input type="text" id="referral-code-input" class="form-input"
               placeholder="<%= t('subscriptions.select.referral_code.placeholder') %>"
               style="text-transform: uppercase; letter-spacing: 1px; flex: 1;">
        <button type="button" id="apply-code-btn" class="button"><%= t('subscriptions.select.referral_code.apply') %></button>
      </div>

      <div id="referral-feedback" style="display: none; margin-top: 1rem;"></div>
    </div>

    <!-- Subscription Tiers -->
    <div class="subscription-tiers">
      <!-- Free Tier -->
      <div class="subscription-tier <%= 'current-tier' if current_user.tier_free? %>">
        <div class="tier-header">
          <h3><%= t('subscriptions.select.tiers.free.name') %></h3>
          <div class="tier-price">
            <%= t('subscriptions.select.tiers.free.price') %><span class="price-period"><%= t('subscriptions.select.tiers.free.period') %></span>
          </div>
        </div>
        <div class="tier-features">
          <% t('subscriptions.select.tiers.free.features').each_with_index do |feature, index| %>
            <div class="feature-item <%= index < 3 ? 'feature-included' : 'feature-excluded' %>">
              <%= index < 3 ? '✓' : '✗' %> <%= feature %>
            </div>
          <% end %>
        </div>
        <div class="tier-action">
          <% if current_user.tier_free? %>
            <button class="button button-secondary" disabled><%= t('subscriptions.select.tiers.free.current') %></button>
          <% else %>
            <%= form_with url: choose_subscription_path, method: :post, local: true do |form| %>
              <%= form.hidden_field :tier, value: 'free' %>
              <%= form.submit t('subscriptions.select.tiers.free.choose'), class: "button" %>
            <% end %>
          <% end %>
        </div>
      </div>

      <!-- Premium Tier -->
      <div class="subscription-tier premium-tier <%= 'current-tier' if current_user.tier_premium? %>">
        <div class="tier-header premium-header">
          <h3>
            <%= t('subscriptions.select.tiers.premium.name') %>
            <span class="tag attention"><%= t('subscriptions.select.tiers.premium.recommended') %></span>
          </h3>
          <div id="premium-pricing" class="tier-price">
            <%= t('subscriptions.select.tiers.premium.price') %><span class="price-period"><%= t('subscriptions.select.tiers.premium.period') %></span>
          </div>
        </div>
        <div class="tier-features">
          <% t('subscriptions.select.tiers.premium.features').each do |feature| %>
            <div class="feature-item feature-included">
              ✓ <%= feature %>
            </div>
          <% end %>
        </div>
        <div class="tier-action">
          <% if current_user.tier_premium? %>
            <button class="button button-secondary" disabled><%= t('subscriptions.select.tiers.premium.current') %></button>
          <% else %>
            <button type="button" id="choose-premium-btn" class="button button-primary">
              <%= t('subscriptions.select.tiers.premium.choose') %>
            </button>
          <% end %>
        </div>
      </div>
    </div>

    <!-- Additional Information -->
    <div class="form-card bg-light">
      <h3><%= t('subscriptions.select.info.title') %></h3>
      <div class="info-grid">
        <% t('subscriptions.select.info.features').each do |feature| %>
          <div class="info-item"><%= feature %></div>
        <% end %>
      </div>
    </div>
  </div>
</div>

<!-- Hidden form for premium selection -->
<%= form_with url: choose_subscription_path, method: :post, local: true, id: "premium-form", class: "hidden" do |form| %>
  <%= form.hidden_field :tier, value: 'premium' %>
  <%= form.hidden_field :referral_code, id: "premium-referral-code" %>
<% end %>

<script>
let appliedReferralCode = null;
const originalPremiumPrice = '<%= t('subscriptions.select.tiers.premium.price') %>';

// Translation strings for JavaScript
const translations = {
  enterCode: '<%= t('subscriptions.select.referral_code.enter_code') %>',
  applying: '<%= t('subscriptions.select.referral_code.applying') %>',
  apply: '<%= t('subscriptions.select.referral_code.apply') %>',
  error: '<%= t('subscriptions.select.referral_code.error') %>'
};

document.getElementById('apply-code-btn').addEventListener('click', function() {
  const code = document.getElementById('referral-code-input').value.trim().toUpperCase();

  if (!code) {
    showFeedback(translations.enterCode, 'error');
    return;
  }

  // Show loading state
  this.textContent = translations.applying;
  this.disabled = true;

  // AJAX call to validate referral code
  fetch('<%= validate_referral_code_path %>', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
    },
    body: JSON.stringify({ code: code })
  })
  .then(response => response.json())
  .then(data => {
    if (data.success) {
      appliedReferralCode = code;
      updatePremiumPricing(data.discount);
      showFeedback(data.message, 'success');
    } else {
      showFeedback(data.message, 'error');
    }
  })
  .catch(error => {
    showFeedback(translations.error, 'error');
  })
  .finally(() => {
    // Reset button state
    document.getElementById('apply-code-btn').textContent = translations.apply;
    document.getElementById('apply-code-btn').disabled = false;
  });
});

function showFeedback(message, type) {
  const feedback = document.getElementById('referral-feedback');
  feedback.className = type === 'success' ? 'tag ready' : 'tag attention';
  feedback.textContent = message;
  feedback.style.display = 'block';
}

function updatePremiumPricing(discount) {
  const pricingDiv = document.getElementById('premium-pricing');
  const period = '<%= t('subscriptions.select.tiers.premium.period') %>';

  if (discount.final_price === 0) {
    // Free upgrade
    pricingDiv.innerHTML = `
      FREE<span class="price-period">${period}</span>
      <div class="original-price">${originalPremiumPrice} - ${discount.description}</div>
    `;
  } else {
    // Discounted price
    pricingDiv.innerHTML = `
      €${discount.final_price}<span class="price-period">${period}</span>
      <div class="original-price">${originalPremiumPrice} - ${discount.description}</div>
    `;
  }
}

document.getElementById('choose-premium-btn').addEventListener('click', function() {
  // Set referral code in hidden form if applied
  if (appliedReferralCode) {
    document.getElementById('premium-referral-code').value = appliedReferralCode;
  }

  // Submit the premium form (will be handled by subscription team's payment flow)
  document.getElementById('premium-form').submit();
});

// Allow Enter key to apply referral code
document.getElementById('referral-code-input').addEventListener('keypress', function(e) {
  if (e.key === 'Enter') {
    document.getElementById('apply-code-btn').click();
  }
});
</script>

<style>
  .subscription-tiers {
    display: flex;
    gap: 2rem;
    margin: 2rem 0;
  }

  .subscription-tier {
    flex: 1;
    background: white;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    overflow: hidden;
  }

  .current-tier {
    border: 2px solid #17B7B7;
  }

  .premium-tier {
    border: 2px solid #FFD06B;
  }

  .tier-header {
    padding: 1.5rem 1rem;
    text-align: center;
    border-bottom: 1px solid #e0e0e0;
  }

  .premium-header {
    background-color: #FFF9E8;
  }

  .tier-header h3 {
    margin-bottom: 0.5rem;
    color: #282828;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
  }

  .tier-price {
    font-size: 2rem;
    font-weight: 600;
    color: #2271B1;
  }

  .price-period {
    font-size: 1rem;
    color: #555555;
  }

  .original-price {
    font-size: 0.9rem;
    color: #666;
    text-decoration: line-through;
    margin-top: 0.25rem;
  }

  .tier-features {
    padding: 1.5rem 1rem;
  }

  .feature-item {
    padding: 0.5rem 0;
    font-size: 1rem;
  }

  .feature-included {
    color: #209903;
  }

  .feature-excluded {
    color: #999;
  }

  .tier-action {
    padding: 1rem;
    text-align: center;
    border-top: 1px solid #e0e0e0;
  }

  .info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
  }

  .info-item {
    padding: 0.5rem 0;
    font-size: 0.9rem;
  }

  @media (max-width: 1080px) {
    .subscription-tiers {
      flex-direction: column;
      gap: 1rem;
    }

    .info-grid {
      grid-template-columns: 1fr;
    }
  }
</style>
