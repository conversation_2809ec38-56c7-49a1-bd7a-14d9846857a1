<!-- Title Box -->
<div class="project-title-box">
  <!-- <PERSON>er Bar -->
  <div class="title-header-bar"></div>
  
  <!-- Project Title -->
  <div class="title-content">
    <h1><%= h(project.summary) %></h1>
    <p class="location-text">
      <%= heroicon "map-pin", variant: :outline, options: { class: "icon-16" } %>
      <%= project.location %>
    </p>
  </div>
</div>

<!-- Content Grid Container -->
<div class="project-content-grid">
  <!-- Left Content Box -->
  <div class="project-content-left-box">
    <div class="detail-label"><%= t('models.project.attributes.full_description', default: 'Popis ponuky') %></div>
    <div class="description-text">
      <%= simple_format(h(project.full_description)) %>
    </div>

    <div class="project-details-grid">
      <div class="detail-item">
        <div class="detail-label"><%= t('models.project.attributes.project_type', default: 'Typ ponuky') %></div>
        <div class="detail-value"><%= project.translated_project_type&.humanize&.capitalize || t('common.not_specified', default: 'Nešpecifikované') %></div>
      </div>
      <div class="detail-item">
        <div class="detail-label"><%= t('models.project.attributes.category', default: 'Kategória') %></div>
        <div class="detail-value"><%= project.translated_category&.humanize&.capitalize || t('common.not_specified', default: 'Nešpecifikované') %></div>
      </div>
      <div class="detail-item">
        <div class="detail-label"><%= t('models.project.attributes.subcategory', default: 'Podkategória') %></div>
        <div class="detail-value"><%= project.translated_subcategory&.humanize&.capitalize || t('common.not_specified', default: 'Nešpecifikované') %></div>
      </div>
      
      <% if project.real_estate? && (project.land_area.present? || project.area_unit.present?) %>
        <div class="detail-item">
          <div class="detail-label"><%= t('models.project.attributes.land_area', default: 'Plocha') %></div>
          <div class="detail-value">
            <%= format_area_display(project.land_area, project.area_unit) %>
          </div>
        </div>
      <% end %>
    </div>
  </div>

  <!-- Right Content Box -->
  <div class="project-content-right-box">
    <% if project.price_value.present? %>
      <div class="price-display">
        € <%= format_price(project.price_value) %>
        <% if project.price_text.present? %>
          <span class="price-unit">/ <%= project.price_text %></span>
        <% end %>
      </div>
    <% end %>
    
    <div class="info-rows">
      <% if project.commission.present? %>
        <div class="info-row">
          <span class="info-label"><%= t('models.project.attributes.commission', default: 'Provízia') %></span>
          <span class="info-value"><%= format_price(project.commission) %> <%= project.commission_type %></span>
        </div>
      <% end %>
      <div class="info-row">
        <span class="info-label"><%= t('models.user_profile.project_owner', default: 'Ponúka') %></span>
        <span class="info-value"><%= project.user.user_profile.first_name %> <%= project.user.user_profile.last_name %></span>
      </div>
      <% if project.user.user_profile.phone.present? %>
        <div class="info-row">
          <span class="info-label">Tel</span>
          <span class="info-value"><%= project.user.user_profile.phone %></span>
        </div>
      <% end %>
      <% if project.user.user_profile.email.present? %>
        <div class="info-row">
          <span class="info-label">Email</span>
          <span class="info-value"><%= project.user.user_profile.email %></span>
        </div>
      <% end %>
      <div class="info-row">
        <span class="info-label"><%= t('projects.form.last_update', default: 'Aktualizácia') %></span>
        <span class="info-value"><%= project.updated_at&.strftime("%d. %m. %Y") %></span>
      </div>
      <% if current_user == project.user %>
        <div class="info-row">
          <span class="info-label"><%= t('projects.form.visibility', default: 'Viditeľnosť') %></span>
          <span class="info-value">
            <% if project.summary_only %>
              <%= t('projects.common.title_only', default: 'Len názov') %>
            <% else %>
              <%= t('projects.common.everything', default: 'Všetko') %>
            <% end %>
            <% if project.semi_public %>
              <%= t('projects.common.everyone', default: '/Všetci') %>
            <% else %>
              <%= t('projects.common.my_network', default: '/Moja sieť') %>
            <% end %>
          </span>
        </div>
      <% end %>
    </div>

    <% if project.user == current_user %>
      <%= link_to t('projects.edit'), edit_project_path(project), class: "action-button" %>
    <% end %>
  </div>
</div>

<!-- Attachments Box -->
<% if @private_files&.attached? %>
  <div class="project-attachments-box">
    <h2><%= t('projects.form.attached_files', default: 'Prílohy') %></h2>
    
    <div id="downloadOverlay" class="download-overlay" style="display: none;">
      <span>
        <%= t('projects.form.downloading_file', default: 'Sťahovanie súboru') %> <span class="loading-dots"></span>
      </span>
    </div>

    <div class="file-viewer-container flex g1 mb-4">
      <div class="files-box">
      
        <div class="file-grid">
          <% project.private_files.each do |file| %>
            <div class="file-item" 
                data-file-id="<%= file.id %>"
                data-file-hash="<%= project.generate_secure_file_hash(file) %>"
                data-content-type="<%= file.content_type %>"
                data-project-id="<%= project.id %>">
              
              <div class="file-thumbnail-container">
                <% if supports_thumbnail?(file) %>
                  <% thumbnail_url = safe_thumbnail_url(project, file) %>
                  <% if thumbnail_url %>
                    <img src="<%= thumbnail_url %>" 
                        alt="<%= file.filename %>"
                        class="file-thumbnail"
                        data-action="inline"
                        data-inline-url="<%= safe_inline_url(project, file) %>" />
                  <% else %>
                    <!-- Fallback if thumbnail generation fails -->
                    <div class="file-icon file-thumbnail-placeholder file-thumbnail <%= file_icon_class(file) %>" 
                        data-action="inline"
                        data-inline-url="<%= safe_inline_url(project, file) %>">
                      <% if file.image? %>
                        <%= heroicon "photo", variant: :outline, options: { class: "icon-48" } %>
                      <% elsif file.content_type == 'application/pdf' %>
                        <%= heroicon "document-text", variant: :outline, options: { class: "icon-48" } %>
                      <% else %>
                        <%= heroicon "document", variant: :outline, options: { class: "icon-48" } %>
                      <% end %>
                    </div>
                  <% end %>
                <% else %>
                  <!-- Non-thumbnailable files show icon -->
                  <div class="file-icon downloadButton <%= file_icon_class(file) %>"
                      data-file-hash="<%= project.generate_secure_file_hash(file) %>"
                      data-project-id="<%= project.id %>">
                    <%= heroicon "document", variant: :outline, options: { class: "icon-48" } %>
                  </div>
                <% end %>
              </div>
              
              <!-- File Name -->
              <div class="file-name">
                <%= truncate(file.filename.to_s, length: 25) %>
              </div>
              
              <!-- File Meta -->
              <div class="file-meta">
                <span class="file-size"><%= number_to_human_size(file.byte_size, precision: 2) %></span>
                <span class="file-date"><%= file.created_at.strftime("%b %d") %></span>
              </div>
              
              <!-- Download Link -->
              <div class="file-actions">
                <% download_url = safe_download_url(project, file) %>
                <% if download_url %>
                  <a href="<%= download_url %>" class="download-link downloadButton"
                    data-file-hash="<%= project.generate_secure_file_hash(file) %>"
                    data-project-id="<%= project.id %>">
                    <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                      <path d="M21 15v4a2 2 0 01-2 2H5a2 2 0 01-2-2v-4M7 10l5 5 5-5M12 15V3" />
                    </svg>
                    <%= t('common.uploads.actions.download', default: 'Stiahnuť') %>
                  </a>
                <% end %>
              </div>
            </div>
          <% end %>
        </div>
        
        <!-- Inline File Viewer (hidden by default) -->
        <div id="inline-file-viewer" class="inline-file-viewer" style="display: none;">
          <div class="inline-viewer-header">
            <h3 id="inline-viewer-title">File</h3>
            <button id="close-inline-viewer" class="close-btn" type="button">
              <%= heroicon "x-mark", variant: :outline, options: { class: "icon-24" } %>
            </button>
          </div>
          <div id="inline-viewer-content" class="inline-viewer-content">
            <!-- Content will be dynamically loaded here -->
          </div>
        </div>
        
        <!-- Template for sidebar download link -->
        <template id="sidebar-download-template">
          <a href="#" class="sidebar-download-link downloadButton">
            <%= heroicon "arrow-down-circle", variant: :outline, options: { class: "icon-16" } %>
            <%= t('common.uploads.actions.download', default: 'Stiahnuť') %>
          </a>
        </template>
        
        <!-- Store localized text for JavaScript -->
        <div id="js-translations" style="display: none;" 
             data-downloading-text="<%= t('projects.form.downloading_file', default: 'Sťahovanie súboru') %>">
        </div>
      </div>

      <div class="side-right">
      </div>
    </div>
  </div>
<% end %>