<% content_for :title, "Redeem Referral Code" %>

<div class="container">
  <div class="row justify-content-center">
    <div class="col-md-6">
      <div class="card">
        <div class="card-header text-center">
          <h3>
            <i class="fas fa-ticket-alt text-primary"></i>
            Redeem Referral Code
          </h3>
        </div>
        <div class="card-body">
          
          <!-- Current Status -->
          <div class="alert alert-<%= tier_badge_class(current_user.subscription_tier) %>">
            <div class="d-flex align-items-center">
              <div class="mr-3">
                <i class="fas fa-user-circle fa-2x"></i>
              </div>
              <div>
                <strong>Current Plan: <%= current_user.subscription_tier.humanize %></strong>
                <% if current_user.tier_premium? && current_user.subscription_expires_at.present? %>
                  <br>
                  <small>
                    <% if current_user.subscription_expires_at > Time.current %>
                      Expires: <%= current_user.subscription_expires_at.strftime("%B %d, %Y") %>
                    <% else %>
                      <span class="text-danger">Expired on: <%= current_user.subscription_expires_at.strftime("%B %d, %Y") %></span>
                    <% end %>
                  </small>
                <% elsif current_user.tier_pilot? %>
                  <br>
                  <small>Permanent access</small>
                <% end %>
              </div>
            </div>
          </div>

          <!-- Redemption Form -->
          <%= form_with url: redeem_referral_code_path, method: :post, local: true do |form| %>
            <div class="form-group">
              <%= form.label :code, "Referral Code" %>
              <%= form.text_field :code, 
                  class: "form-control form-control-lg text-center", 
                  placeholder: "Enter your code here",
                  required: true,
                  style: "text-transform: uppercase; letter-spacing: 2px; font-family: monospace;",
                  maxlength: 12 %>
              <small class="form-text text-muted">
                Enter the referral code exactly as provided to you.
              </small>
            </div>
            
            <div class="form-group text-center">
              <%= form.submit "Redeem Code", class: "btn btn-primary btn-lg btn-block" %>
            </div>
          <% end %>

          <!-- Information Section -->
          <div class="mt-4">
            <h6><i class="fas fa-info-circle text-info"></i> How Referral Codes Work</h6>
            <ul class="small text-muted">
              <li>Referral codes provide instant access to premium features</li>
              <li>Each code can only be used a limited number of times</li>
              <li>Premium upgrades include access to all platform features</li>
              <li>Pilot codes provide permanent access without expiration</li>
            </ul>
          </div>

          <!-- Benefits Preview -->
          <% if current_user.tier_free? %>
            <div class="mt-4">
              <h6><i class="fas fa-crown text-warning"></i> Premium Benefits</h6>
              <div class="row">
                <div class="col-6">
                  <small class="text-muted">
                    <i class="fas fa-check text-success"></i> Create projects<br>
                    <i class="fas fa-check text-success"></i> Upload files<br>
                  </small>
                </div>
                <div class="col-6">
                  <small class="text-muted">
                    <i class="fas fa-check text-success"></i> Full network access<br>
                    <i class="fas fa-check text-success"></i> Priority support<br>
                  </small>
                </div>
              </div>
            </div>
          <% end %>

          <!-- Back Link -->
          <div class="text-center mt-4">
            <%= link_to "← Back to Dashboard", root_path, class: "btn btn-outline-secondary" %>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<style>
.card {
  box-shadow: 0 4px 8px rgba(0,0,0,0.1);
  border: none;
}

.card-header {
  background: linear-gradient(135deg, #007bff, #0056b3);
  color: white;
  border-bottom: none;
}

.form-control-lg {
  font-size: 1.2rem;
  padding: 0.75rem 1rem;
}

.btn-lg {
  padding: 0.75rem 1.5rem;
  font-size: 1.1rem;
}
</style>