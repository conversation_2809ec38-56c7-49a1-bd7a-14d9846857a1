# ABOUTME: Custom ActiveRecord attribute type for decimal fields that supports Slovak comma separators
# ABOUTME: Handles both comma and period decimal separators, removes spaces for thousands separators

class DecimalWithCommaType < ActiveRecord::Type::Decimal
  def cast(value)
    return super if value.is_a?(Numeric) || value.blank?
    
    # Handle both comma and dot as decimal separators, remove spaces (thousands)
    # Examples: "1,50" → "1.50", "1 500,75" → "1500.75", "1.500,75" → "1500.75"
    sanitized_value = value.to_s
      .gsub(/\s+/, '')           # Remove all spaces (thousands separators)
      .gsub(/\.(?=.*,)/, '')     # Remove dots if there's a comma later (European format: 1.500,75)
      .gsub(',', '.')            # Convert comma to dot for decimal separator
    
    super(sanitized_value)
  end
end