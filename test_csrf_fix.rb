#!/usr/bin/env ruby

# ABOUTME: Manual test script to verify CSRF token fix for project file uploads
# ABOUTME: Simulates user flow: create project -> immediate file upload capability

require 'net/http'
require 'uri'
require 'json'

puts "🧪 Testing CSRF Token Fix for Project File Uploads"
puts "=" * 50

base_url = "http://************:5000"

# Test 1: GET /projects/new - Should now create project immediately
puts "\n1️⃣ Testing immediate project creation on /projects/new"
uri = URI("#{base_url}/projects/new")
begin
  response = Net::HTTP.get_response(uri)
  
  if response.code == "302" || response.code == "200"
    puts "✅ /projects/new responded with #{response.code}"
    puts "   Location: #{response['location']}" if response['location']
    
    # If redirected to edit path, extract project ID
    if response['location'] && response['location'].include?('/projects/') && response['location'].include?('/edit')
      project_id = response['location'].match(/\/projects\/(\d+)\/edit/)[1]
      puts "   🎯 Project created immediately with ID: #{project_id}"
      puts "   ✅ EXPECTED: Immediate project creation working"
    else
      puts "   ⚠️  No redirect to edit - might still be rendering form"
    end
  else
    puts "❌ /projects/new failed with status #{response.code}"
  end
rescue => e
  puts "❌ Error testing /projects/new: #{e.message}"
end

# Test 2: Check form structure expects persisted project
puts "\n2️⃣ Testing form expects persisted project (data-project-id set)"
uri = URI("#{base_url}/projects/new")
begin
  response = Net::HTTP.get_response(uri)
  
  if response.body.include?('data-project-id') 
    puts "✅ Form includes data-project-id attribute"
    
    # Extract project ID from form if present
    if match = response.body.match(/data-project-id=['"](\d+)['"]/)
      project_id = match[1]
      puts "   🎯 Form has project ID: #{project_id}"
      puts "   ✅ EXPECTED: Upload handler can immediately access target_id"
    elsif response.body.include?('data-project-id=""') || response.body.include?("data-project-id=''")
      puts "   ⚠️  Form has empty data-project-id (deferred creation behavior)"
      puts "   ❌ PROBLEM: Upload handler will fail validation"
    end
  else
    puts "❌ Form missing data-project-id attribute"
  end
rescue => e
  puts "❌ Error checking form structure: #{e.message}"
end

puts "\n📋 Test Summary:"
puts "- Immediate project creation on /projects/new"
puts "- Form data-project-id populated from start"
puts "- Upload handler can access target_id immediately"
puts "\n🎯 Expected Result: File uploads work immediately after visiting /projects/new"
puts "   No more 'Please save the project first' error messages"