class NotificationMailerPreview < ActionMailer::Preview
  
  # http://localhost:3000/rails/mailers/notification_mailer/access_request_notification
  def access_request_notification
    project = Project.last
    user = User.last
    NotificationMailer.access_request_notification(project, user)
  end
  
  # http://localhost:3000/rails/mailers/notification_mailer/admin_project_notification
  def admin_project_notification
    project = Project.last
    admin = User.where(role: 'super_boss').first
    current_user = User.last
    NotificationMailer.admin_project_notification(project, admin, current_user)
  end
  
  # http://localhost:3000/rails/mailers/notification_mailer/new_project_notification
  def new_project_notification
    project = Project.first
    user = User.last
    NotificationMailer.new_project_notification(project, user)
  end

  # http://localhost:3000/rails/mailers/notification_mailer/approved_access_notification
  def approved_access_notification
    project = Project.last
    user = User.last
    current_user = User.last
    NotificationMailer.approved_access_notification(project, user, current_user)
  end

  # http://localhost:3000/rails/mailers/notification_mailer/user_approval_request_notification
  def user_approval_request_notification
    user = User.includes(:user_profile).last
    NotificationMailer.user_approval_request_notification(user)
  end

  # http://localhost:3000/rails/mailers/notification_mailer/user_approval_notification
  def user_approval_notification
    # Create a user that matches the current preview locale
    user = User.includes(:user_profile).last
    
    # DEBUG: Log what's happening
    Rails.logger.info "🔍 [PREVIEW DEBUG] I18n.locale = #{I18n.locale}"
    Rails.logger.info "🔍 [PREVIEW DEBUG] User original language = #{user&.user_profile&.default_language}"
    
    # For preview purposes, temporarily override the user's language preference
    # to match the Rails preview locale parameter
    if user&.user_profile
      original_language = user.user_profile.default_language
      user.user_profile.default_language = I18n.locale.to_s
      
      Rails.logger.info "🔍 [PREVIEW DEBUG] Set user language to = #{user.user_profile.default_language}"
      
      mail = NotificationMailer.user_approval_notification(user)
      
      Rails.logger.info "🔍 [PREVIEW DEBUG] Generated subject = #{mail.subject}"
      
      # Restore original language (important for preview, doesn't affect DB)
      user.user_profile.default_language = original_language
      
      return mail
    end
    
    NotificationMailer.user_approval_notification(user)
  end


end 