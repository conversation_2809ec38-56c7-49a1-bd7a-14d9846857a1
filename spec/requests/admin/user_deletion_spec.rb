# ABOUTME: Test for admin user deletion functionality with confirmation
# ABOUTME: Ensures proper authorization, cascading deletion, and UI confirmation flow

require 'rails_helper'

RSpec.describe "Admin User Deletion", type: :request do
  let(:admin_user) { create(:user, role: :super_boss, approved: true) }
  let(:target_user) { create(:user, approved: true) }

  before do
    # Ensure admin user has complete profile to avoid redirects
    admin_user.user_profile.update!(
      first_name: 'Admin',
      last_name: 'User', 
      city: 'Bratislava',
      country: 'Slovakia'
    )
    
    # Ensure target user has complete profile
    target_user.user_profile.update!(
      first_name: 'Target',
      last_name: 'User',
      city: 'Bratislava', 
      country: 'Slovakia'
    )
    
    sign_in admin_user
  end

  describe "DELETE /admin/subscriptions/:id" do
    it "requires admin authorization" do
      regular_user = create(:user, approved: true)
      regular_user.user_profile.update!(
        first_name: 'Regular',
        last_name: 'User',
        city: 'Bratislava',
        country: 'Slovakia'
      )
      sign_in regular_user
      
      delete "/admin/subscriptions/#{target_user.id}"
      expect(response).to redirect_to(authenticated_root_path)
    end

    it "deletes user and all associated data when confirmed" do
      # Create associated data, specifying user explicitly to avoid factory-generated users
      project = build(:project)
      project.user = target_user
      project.save!
      
      want = build(:want)
      want.user = target_user
      want.save!
      
      connection = build(:network_connection)
      connection.inviter = target_user
      connection.invitee = create(:user) # This will create one additional user
      connection.save!
      
      initial_user_count = User.count
      
      expect {
        delete "/admin/subscriptions/#{target_user.id}", params: { confirm_deletion: "DELETE #{target_user.email}" }
      }.to change(User, :count).by(-1)
        .and change(Project, :count).by(-1)
        .and change(Want, :count).by(-1)
        .and change(NetworkConnection, :count).by(-1)

      expect(response).to redirect_to(admin_subscriptions_path)
      expect(flash[:notice]).to include("User #{target_user.email} has been deleted")
    end

    it "does not delete user without proper confirmation" do
      initial_count = User.count
      
      delete "/admin/subscriptions/#{target_user.id}"
      
      expect(User.count).to eq(initial_count)
      expect(response).to redirect_to(admin_subscriptions_path)
      expect(flash[:alert]).to include("Deletion cancelled")
    end

    it "does not delete user with incorrect confirmation text" do
      initial_count = User.count
      
      delete "/admin/subscriptions/#{target_user.id}", params: { confirm_deletion: "wrong text" }
      
      expect(User.count).to eq(initial_count)
      expect(response).to redirect_to(admin_subscriptions_path)
      expect(flash[:alert]).to include("Confirmation text does not match")
    end

    it "prevents admin from deleting themselves" do
      expect {
        delete "/admin/subscriptions/#{admin_user.id}", params: { confirm_deletion: "DELETE #{admin_user.email}" }
      }.not_to change(User, :count)

      expect(response).to redirect_to(admin_subscriptions_path)
      expect(flash[:alert]).to include("Cannot delete your own account")
    end
  end
end