# ABOUTME: System tests for Google Maps API integration in Want form location fields (UNL-58)
# ABOUTME: Tests Google Maps autocomplete functionality for want_place field to ensure parity with project location

require 'rails_helper'

RSpec.describe "Google Maps Want Location Integration (UNL-58)", type: :feature do
  let(:user) { create(:user) }
  
  before do
    sign_in user
    
    # Ensure user has complete profile to avoid redirection
    user.user_profile.update!(
      first_name: 'Test',
      last_name: 'User',
      city: 'Bratislava',
      country: 'Slovakia'
    ) if user.user_profile
  end

  scenario "Want form has place field with correct ID for Google Maps integration" do
    visit new_want_path
    
    # Just verify the form renders and has the correct field ID
    expect(page).to have_field('Place')
    expect(page).to have_css('#want_place')
  end

  scenario "JavaScript includes want_place in locationInputs array" do
    visit new_want_path
    
    # Check that the JavaScript on the page includes the want_place field
    # This is a simplified check - in a real browser the Google Maps would initialize
    expect(page).to have_css('#want_place[autocomplete="off"]')
  end
end