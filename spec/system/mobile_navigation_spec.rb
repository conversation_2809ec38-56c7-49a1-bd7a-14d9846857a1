# ABOUTME: System test to verify mobile navigation menu is scrollable when content overflows viewport
# ABOUTME: This test ensures users can access all navigation items on mobile devices

require 'rails_helper'

RSpec.describe 'Mobile Navigation', type: :system do
  let(:user) { create(:user) }

  before do
    driven_by(:selenium_chrome_headless)
  end

  describe 'scrollable navigation menu' do
    context 'when logged in on mobile viewport' do
      before do
        sign_in user
        # Set mobile viewport size (iPhone SE)
        page.driver.browser.manage.window.resize_to(375, 667)
        visit root_path
      end

      it 'allows scrolling within mobile navigation when menu is open' do
        # Open mobile navigation
        find('#navbarToggle').click
        
        # Wait for sidebar to become visible
        expect(page).to have_css('.sidebar.mobile-visible')
        
        # Check that sidebar-nav has overflow scrolling enabled
        sidebar_nav = find('.sidebar-nav')
        
        # Test CSS properties for scrolling capability
        expect(sidebar_nav).to have_css('overflow-y', 'auto')
        expect(sidebar_nav).to have_css('-webkit-overflow-scrolling', 'touch')
        
        # Verify max-height constraint exists
        expect(sidebar_nav).to have_css('max-height')
        
        # Test that we can scroll within navigation (if content overflows)
        # This simulates having many nav items that would overflow viewport
        page.execute_script("
          const nav = document.querySelector('.sidebar-nav');
          nav.innerHTML = nav.innerHTML + '<div class=\"nav-item\">Test Item 1</div>'.repeat(20);
        ")
        
        # Should be able to scroll within the navigation
        expect(page.execute_script("
          const nav = document.querySelector('.sidebar-nav');
          return nav.scrollHeight > nav.clientHeight;
        ")).to be true
        
        # Test actual scrolling behavior
        page.execute_script("
          const nav = document.querySelector('.sidebar-nav');
          nav.scrollTop = 100;
        ")
        
        scroll_position = page.execute_script("
          return document.querySelector('.sidebar-nav').scrollTop;
        ")
        expect(scroll_position).to be > 0
      end
    end
  end
end