# ABOUTME: Test for real estate area and unit fields display in project preview/show pages
# ABOUTME: Ensures that land_area and area_unit fields are properly displayed for real estate projects

require 'rails_helper'

RSpec.feature "Real Estate Fields Display", type: :feature do
  around do |example|
    I18n.with_locale(:sk) { example.run }
  end
  let(:user) { create(:user) }
  let(:admin_user) { create(:user, :super_boss) }
  
  before do
    # Ensure user profile is complete and user is approved
    user.user_profile.update!(
      first_name: 'Test',
      last_name: 'User',
      city: 'Bratislava',
      country: 'Slovakia',
      bio: 'Test bio',
      email: user.email,
      phone: '+421900123456',
      profile_completed: true
    )
    user.update!(approved: true)
  end
  
  scenario "displays land area and unit fields for real estate projects" do
    login_as user, scope: :user
    
    # Create a real estate project with area fields
    project = create(:project,
      user: user,
      summary: "Beautiful commercial property",
      project_type: :real_estate,
      category: :commercial_property,
      subcategory: :industrial_building,
      location: "Bratislava, Slovakia",
      land_area: 1500,
      area_unit: "m²",
      project_status: true,
      full_access: true,
      semi_public: true,
      network_only: false
    )
    
    # Approve the project as admin
    project.admin_approver = admin_user
    project.update!(approved: true)
    
    visit project_path(project, locale: :sk)
    
    # The area and unit should be displayed in the project details
    # Using the actual localized text that appears on the page
    expect(page).to have_text("Area")  # Translation key displays "Area" in current setup
    expect(page).to have_text("1500 m2")
  end
  
  scenario "does not display area fields for non-real estate projects" do
    login_as user, scope: :user
    
    # Create a business project (non-real estate)
    project = create(:project,
      user: user,
      summary: "Tech startup opportunity",
      project_type: :business,
      category: :business_acquisition,
      subcategory: :asset_purchase,  # Fix subcategory to match business_acquisition
      location: "Bratislava, Slovakia",
      land_area: 500,  # These should be ignored for non-real estate
      area_unit: "m²",
      project_status: true,
      full_access: true,
      semi_public: true,
      network_only: false
    )
    
    # Approve the project as admin
    project.admin_approver = admin_user
    project.update!(approved: true)
    
    visit project_path(project, locale: :sk)
    
    # Area fields should NOT be displayed for non-real estate projects
    expect(page).not_to have_text("Area")
    expect(page).not_to have_text("500 m2")
  end
  
  scenario "handles empty area fields gracefully" do
    login_as user, scope: :user
    
    # Create a real estate project without area fields
    project = create(:project,
      user: user,
      summary: "Land opportunity",
      project_type: :real_estate,
      category: :land,
      subcategory: :buildable,
      location: "Bratislava, Slovakia",
      project_status: true,
      full_access: true,
      semi_public: true,
      network_only: false
    )
    
    # Approve the project as admin
    project.admin_approver = admin_user
    project.update!(approved: true)
    
    visit project_path(project, locale: :sk)
    
    # Should show the area label but handle empty values gracefully
    # The area section should NOT appear for projects without area/unit data
    expect(page).not_to have_text("Area")
    expect(page).not_to have_text("m2")  # Unit should not show if area is empty
  end
  
end