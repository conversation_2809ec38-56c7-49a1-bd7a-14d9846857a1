# ABOUTME: Feature test to verify mobile navigation CSS implementation allows scrolling
# ABOUTME: This test validates the CSS properties needed for mobile navigation scrollability

require 'rails_helper'

RSpec.describe 'Mobile Navigation Scrolling', type: :feature do
  let(:user) { create(:user) }

  describe 'CSS implementation for mobile navigation scrolling' do
    context 'when viewing application stylesheets' do
      it 'should have overflow scrolling properties for mobile sidebar navigation' do
        # Read the application.scss file
        css_content = File.read(Rails.root.join('app', 'assets', 'stylesheets', 'application.scss'))
        
        # Should contain mobile responsive section with sidebar-nav scrolling
        expect(css_content).to include('@media (max-width: $breakpoint-mobile)')
        
        # Check for mobile section with our specific fix
        # Look for the @media section starting at line 865
        mobile_section_start = css_content.index('// Mobile Responsive Styles')
        next_major_section = css_content.index('@media (max-width: $breakpoint-mobile)', mobile_section_start + 200) # Skip the first one
        
        if mobile_section_start && next_major_section
          # Get a reasonable chunk that includes our CSS
          mobile_section = css_content[mobile_section_start..mobile_section_start + 1000]
        else
          mobile_section = css_content
        end
        
        # Should have scrolling properties for sidebar-nav within .sidebar
        expect(mobile_section).to include('.sidebar')
        expect(mobile_section).to include('.sidebar-nav')
        expect(mobile_section).to include('overflow-y: auto')
        expect(mobile_section).to include('-webkit-overflow-scrolling: touch')
        expect(mobile_section).to include('max-height: calc(100vh - 80px)')
        expect(mobile_section).to include('padding-bottom: 20px')
      end
    end
  end
end