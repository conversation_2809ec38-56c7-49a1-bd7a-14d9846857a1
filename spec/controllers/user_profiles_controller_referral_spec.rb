require 'rails_helper'

RSpec.describe UserProfilesController, type: :controller do
  let(:user) { create(:user) }
  let(:admin_user) { create(:user, role: :super_boss) }

  before do
    sign_in user
  end

  describe "POST #validate_referral_code" do
    context "with valid referral code" do
      let!(:referral_code) { create(:referral_code, code: "TESTCODE", discount_percentage: 50.0, created_by: admin_user) }

      it "returns success with discount information for partial discount" do
        post :validate_referral_code, params: { code: "TESTCODE" }
        
        expect(response).to have_http_status(:success)
        json_response = JSON.parse(response.body)
        
        expect(json_response['success']).to be true
        expect(json_response['discount']['type']).to eq('percentage_discount')
        expect(json_response['discount']['discount_percentage']).to eq("50.0")
        expect(json_response['discount']['final_price']).to eq("15.0") # 50% of 29.99, rounded
        expect(json_response['discount']['original_price']).to eq(29.99)
      end

      it "returns success with free upgrade information for 100% discount" do
        referral_code.update!(discount_percentage: 100.0)
        post :validate_referral_code, params: { code: "TESTCODE" }
        
        expect(response).to have_http_status(:success)
        json_response = JSON.parse(response.body)
        
        expect(json_response['success']).to be true
        expect(json_response['discount']['type']).to eq('free_upgrade')
        expect(json_response['discount']['discount_percentage']).to eq("100.0")
        expect(json_response['discount']['final_price']).to eq(0)
      end

      it "returns success with no discount for 0% discount" do
        referral_code.update!(discount_percentage: 0.0)
        post :validate_referral_code, params: { code: "TESTCODE" }
        
        expect(response).to have_http_status(:success)
        json_response = JSON.parse(response.body)
        
        expect(json_response['success']).to be true
        expect(json_response['discount']['type']).to eq('none')
        expect(json_response['discount']['discount_percentage']).to eq(0)
        expect(json_response['discount']['final_price']).to eq(29.99)
      end
    end

    context "with case insensitive code" do
      let!(:referral_code) { create(:referral_code, code: "TESTCODE", discount_percentage: 25.0, created_by: admin_user) }

      it "finds code regardless of case" do
        post :validate_referral_code, params: { code: "testcode" }
        
        expect(response).to have_http_status(:success)
        json_response = JSON.parse(response.body)
        
        expect(json_response['success']).to be true
        expect(json_response['discount']['discount_percentage']).to eq("25.0")
      end

      it "finds code with mixed case" do
        post :validate_referral_code, params: { code: "TestCode" }
        
        expect(response).to have_http_status(:success)
        json_response = JSON.parse(response.body)
        
        expect(json_response['success']).to be true
        expect(json_response['discount']['discount_percentage']).to eq("25.0")
      end
    end

    context "with invalid referral code" do
      it "returns error for non-existent code" do
        post :validate_referral_code, params: { code: "INVALID" }
        
        expect(response).to have_http_status(:success)
        json_response = JSON.parse(response.body)
        
        expect(json_response['success']).to be false
        expect(json_response['message']).to be_present
      end

      it "returns error for blank code" do
        post :validate_referral_code, params: { code: "" }
        
        expect(response).to have_http_status(:success)
        json_response = JSON.parse(response.body)
        
        expect(json_response['success']).to be false
        expect(json_response['message']).to be_present
      end
    end

    context "with expired referral code" do
      let!(:referral_code) { create(:referral_code, code: "EXPIRED", expires_at: 1.day.ago, created_by: admin_user) }

      it "returns error for expired code" do
        post :validate_referral_code, params: { code: "EXPIRED" }
        
        expect(response).to have_http_status(:success)
        json_response = JSON.parse(response.body)
        
        expect(json_response['success']).to be false
        expect(json_response['message']).to be_present
      end
    end

    context "with used up referral code" do
      let!(:referral_code) { create(:referral_code, code: "USEDCODE", max_uses: 1, current_uses: 1, created_by: admin_user) }

      it "returns error for used up code" do
        post :validate_referral_code, params: { code: "USEDCODE" }
        
        expect(response).to have_http_status(:success)
        json_response = JSON.parse(response.body)
        
        expect(json_response['success']).to be false
        expect(json_response['message']).to be_present
      end
    end
  end
end
