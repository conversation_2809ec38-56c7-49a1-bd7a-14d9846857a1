# ABOUTME: Comprehensive tests for WantPolicy that verify authorization patterns for all CRUD operations
# ABOUTME: Tests tier-based access restrictions and owner-based permissions

require 'rails_helper'

RSpec.describe WantPolicy, type: :policy do
  let(:pilot_user) { create(:user, subscription_tier: :pilot) }
  let(:premium_user) { create(:user, subscription_tier: :premium) }
  let(:free_user) { create(:user, subscription_tier: :free) }
  let(:want) { create(:want, user: pilot_user) }
  
  before do
    # Ensure all users have complete profiles to avoid redirection issues
    [pilot_user, premium_user, free_user].each do |u|
      u.user_profile.update!(
        first_name: 'Test',
        last_name: 'User',
        city: 'Bratislava', 
        country: 'Slovakia'
      ) if u.user_profile
    end
  end

  describe 'tier access restrictions' do
    describe '#index?' do
      context 'with pilot tier' do
        it 'allows access' do
          policy = described_class.new(want, user: pilot_user)
          expect(policy.apply(:index?)).to be true
        end
      end
      
      context 'with premium tier' do
        it 'denies access' do
          policy = described_class.new(want, user: premium_user)
          expect(policy.apply(:index?)).to be false
        end
      end
      
      context 'with free tier' do
        it 'denies access' do
          policy = described_class.new(want, user: free_user)
          expect(policy.apply(:index?)).to be false
        end
      end
    end

    describe '#create?' do
      context 'with pilot tier' do
        it 'allows access' do
          policy = described_class.new(want, user: pilot_user)
          expect(policy.apply(:create?)).to be true
        end
      end
      
      context 'with premium tier' do
        it 'denies access' do
          policy = described_class.new(want, user: premium_user)
          expect(policy.apply(:create?)).to be false
        end
      end
      
      context 'with free tier' do
        it 'denies access' do
          policy = described_class.new(want, user: free_user)
          expect(policy.apply(:create?)).to be false
        end
      end
    end
  end

  describe 'ownership rules for pilot users' do
    let(:other_pilot) { create(:user, subscription_tier: :pilot) }
    let(:other_want) { create(:want, user: other_pilot) }
    
    before do
      other_pilot.user_profile.update!(
        first_name: 'Other',
        last_name: 'User',
        city: 'Bratislava',
        country: 'Slovakia'
      )
    end
    
    describe '#edit?' do
      it 'allows owner to edit their own want' do
        policy = described_class.new(want, user: pilot_user)
        expect(policy.apply(:edit?)).to be true
      end
      
      it 'denies non-owner from editing others wants' do
        policy = described_class.new(want, user: other_pilot)
        expect(policy.apply(:edit?)).to be false
      end
    end
    
    describe '#update?' do
      it 'allows owner to update their own want' do
        policy = described_class.new(want, user: pilot_user)
        expect(policy.apply(:update?)).to be true
      end
      
      it 'denies non-owner from updating others wants' do
        policy = described_class.new(want, user: other_pilot)
        expect(policy.apply(:update?)).to be false
      end
    end
    
    describe '#destroy?' do
      it 'allows owner to destroy their own want' do
        policy = described_class.new(want, user: pilot_user)
        expect(policy.apply(:destroy?)).to be true
      end
      
      it 'denies non-owner from destroying others wants' do
        policy = described_class.new(want, user: other_pilot)
        expect(policy.apply(:destroy?)).to be false
      end
    end
  end
end