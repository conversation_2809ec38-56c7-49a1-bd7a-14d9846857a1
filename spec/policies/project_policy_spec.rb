require 'rails_helper'

RSpec.describe ProjectPolicy, type: :policy do
  subject(:policy) { described_class.new(project, user: user) }
  
  let(:owner) { create(:user) }
  let(:connected_user) { create(:user) }
  let(:unconnected_user) { create(:user) }
  
  before do
    # Create network connection between owner and connected_user
    create(:network_connection, inviter: owner, invitee: connected_user)
  end

  describe '#view_full_details?' do
    context 'when user is project owner' do
      let(:user) { owner }
      let(:project) { create(:project, user: owner) }

      it 'allows access regardless of sharing settings' do
        expect(policy.view_full_details?).to be true
      end
    end

    context 'when user has explicit ProjectAuth' do
      let(:user) { connected_user }
      let(:project) { create(:project, user: owner) }
      
      before do
        create(:project_auth, project: project, user: connected_user, access_level: 'full_details')
      end

      it 'allows access' do
        expect(policy.view_full_details?).to be true
      end
    end

    context 'with full_access + network_only settings' do
      let(:project) do
        create(:project, 
          user: owner, 
          full_access: true, 
          network_only: true,
          summary_only: false,
          semi_public: false
        )
      end

      context 'when user is connected to owner' do
        let(:user) { connected_user }

        it 'allows access automatically' do
          expect(policy.view_full_details?).to be true
        end
      end

      context 'when user is not connected to owner' do
        let(:user) { unconnected_user }

        it 'denies access' do
          expect(policy.view_full_details?).to be false
        end
      end
    end

    context 'with full_access + semi_public settings' do
      let(:project) do
        create(:project, 
          user: owner, 
          full_access: true, 
          semi_public: true,
          summary_only: false,
          network_only: false
        )
      end

      context 'when user is connected to owner' do
        let(:user) { connected_user }

        it 'allows access automatically' do
          expect(policy.view_full_details?).to be true
        end
      end

      context 'when user is not connected to owner' do
        let(:user) { unconnected_user }

        it 'allows access automatically (public access)' do
          expect(policy.view_full_details?).to be true
        end
      end
    end

    context 'with summary_only settings (traditional behavior)' do
      let(:project) do
        create(:project, 
          user: owner, 
          summary_only: true, 
          network_only: true,
          full_access: false,
          semi_public: false
        )
      end

      context 'when user is connected to owner' do
        let(:user) { connected_user }

        it 'denies access (requires explicit approval)' do
          expect(policy.view_full_details?).to be false
        end
      end

      context 'when user is not connected to owner' do
        let(:user) { unconnected_user }

        it 'denies access' do
          expect(policy.view_full_details?).to be false
        end
      end
    end

    context 'with unapproved projects (approved: false)' do
      let(:project) do
        create(:project, 
          user: owner, 
          full_access: true, 
          semi_public: true, # Most permissive settings
          summary_only: false,
          network_only: false,
          approved: false    # But not approved
        )
      end

      context 'when user is project owner' do
        let(:user) { owner }

        it 'allows access (owners can see their own unapproved projects)' do
          expect(policy.view_full_details?).to be true
        end
      end

      context 'when user is connected to owner' do
        let(:user) { connected_user }

        it 'denies access (unapproved projects not accessible to non-owners)' do
          expect(policy.view_full_details?).to be false
        end
      end

      context 'when user is not connected to owner' do
        let(:user) { unconnected_user }

        it 'denies access' do
          expect(policy.view_full_details?).to be false
        end
      end

      context 'when user has explicit ProjectAuth but project is unapproved' do
        let(:user) { connected_user }
        
        before do
          create(:project_auth, project: project, user: connected_user, access_level: 'full_details')
        end

        it 'denies access (approval status overrides ProjectAuth)' do
          expect(policy.view_full_details?).to be false
        end
      end
    end

    context 'edge cases' do
      context 'when full_access is true but both visibility flags are false' do
        let(:project) do
          create(:project, 
            user: owner, 
            full_access: true,
            summary_only: false,
            network_only: false,
            semi_public: false
          )
        end
        let(:user) { connected_user }

        it 'denies access (no visibility mode selected)' do
          expect(policy.view_full_details?).to be false
        end
      end

      context 'when user has both connection AND explicit ProjectAuth' do
        let(:project) do
          create(:project, 
            user: owner, 
            full_access: true, 
            network_only: true,
            summary_only: false,
            semi_public: false
          )
        end
        let(:user) { connected_user }
        
        before do
          create(:project_auth, project: project, user: connected_user, access_level: 'full_details')
        end

        it 'allows access (explicit auth takes precedence)' do
          expect(policy.view_full_details?).to be true
        end
      end
    end
  end

  describe '#user_connected_to_project_owner?' do
    let(:project) { create(:project, user: owner) }
    let(:policy) { described_class.new(user, project) }

    context 'when user is connected to project owner' do
      let(:user) { connected_user }

      it 'returns true' do
        expect(policy.send(:user_connected_to_project_owner?)).to be true
      end
    end

    context 'when user is not connected to project owner' do
      let(:user) { unconnected_user }

      it 'returns false' do
        expect(policy.send(:user_connected_to_project_owner?)).to be false
      end
    end

    context 'with reverse connection (invitee -> inviter)' do
      let(:user) { create(:user) }
      let(:project) { create(:project, user: owner) }
      
      before do
        # Create connection where owner is invitee, not inviter
        create(:network_connection, inviter: user, invitee: owner)
      end

      it 'returns true (bidirectional check)' do
        expect(policy.send(:user_connected_to_project_owner?)).to be true
      end
    end
  end

  describe 'subscription tier access controls' do
    let(:free_user) { create(:user, subscription_tier: :free) }
    let(:premium_user) { create(:user, subscription_tier: :premium, subscription_expires_at: 1.month.from_now) }
    let(:expired_premium_user) { create(:user, subscription_tier: :premium, subscription_expires_at: 1.day.ago) }
    let(:pilot_user) { create(:user, subscription_tier: :pilot) }
    let(:project) { create(:project, user: owner) }

    describe '#create?' do
      context 'when user has free tier' do
        let(:user) { free_user }

        it 'denies project creation' do
          expect(policy.create?).to be false
        end
      end

      context 'when user has expired premium subscription' do
        let(:user) { expired_premium_user }

        it 'denies project creation' do
          expect(policy.create?).to be false
        end
      end

      context 'when user has active premium subscription' do
        let(:user) { premium_user }

        it 'allows project creation' do
          expect(policy.create?).to be true
        end
      end

      context 'when user has pilot access' do
        let(:user) { pilot_user }

        it 'allows project creation' do
          expect(policy.create?).to be true
        end
      end
    end

    describe '#upload_files?' do
      context 'when user has free tier' do
        let(:user) { free_user }

        it 'denies file upload' do
          expect(policy.upload_files?).to be false
        end
      end

      context 'when user has active premium subscription' do
        let(:user) { premium_user }

        it 'allows file upload' do
          expect(policy.upload_files?).to be true
        end
      end

      context 'when user has pilot access' do
        let(:user) { pilot_user }

        it 'allows file upload' do
          expect(policy.upload_files?).to be true
        end
      end
    end
  end

  describe 'other policy methods (regression test)' do
    let(:user) { owner }
    let(:project) { create(:project, user: owner) }

    it 'does not break existing policy methods' do
      expect(policy.show?).to be true
      expect(policy.edit?).to be true
      expect(policy.update?).to be true
      expect(policy.destroy?).to be true
    end
  end
end