FactoryBot.define do
  factory :referral_code do
    sequence(:code) { |n| "CODE#{n.to_s.rjust(4, '0')}" }
    association :created_by, factory: :user
    status { :active }
    tier_upgrade_to { :premium }
    duration_months { 1 }
    max_uses { 1 }
    current_uses { 0 }
    discount_percentage { 100.0 }
    expires_at { 1.month.from_now }
    description { "Test referral code" }
    
    trait :pilot_upgrade do
      tier_upgrade_to { :pilot }
    end

    trait :partial_discount do
      discount_percentage { 50.0 }
    end

    trait :no_discount do
      discount_percentage { 0.0 }
    end

    trait :expired do
      expires_at { 1.day.ago }
    end

    trait :used_up do
      status { :used_up }
      current_uses { 1 }
    end

    trait :disabled do
      status { :disabled }
    end
  end
end
