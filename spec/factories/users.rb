# spec/factories/users.rb
FactoryBot.define do
  factory :user do
    sequence(:email) { |n| "user#{n}@example.com" }
    password { "password123" }
    password_confirmation { "password123" }
    confirmed_at { Time.current }
    
    trait :admin do
      role { 'admin' }
    end
    
    trait :super_boss do
      role { 'super_boss' }
    end

    trait :pilot do
      subscription_tier { 'pilot' }
    end
    
    # Create user profile automatically
    after(:create) do |user|
      create(:user_profile, user: user) unless user.user_profile
    end
  end
end