# ABOUTME: Test suite for decimal price support feature in Projects and Wants
# ABOUTME: Tests decimal validation, sanitization, and format_price helper functionality

require 'rails_helper'

RSpec.describe 'Decimal Price Support', type: :model do
  include ActionView::Helpers::<PERSON><PERSON>elper
  
  describe 'Project decimal price fields' do
    let(:project) { build(:project) }
    
    context 'price_value field' do
      it 'accepts decimal values' do
        project.price_value = 1500.75
        expect(project).to be_valid
        expect(project.price_value).to eq(1500.75)
      end
      
      it 'sanitizes comma decimal separators' do
        project.price_value = '1500,75'
        project.valid?
        expect(project.price_value).to eq(BigDecimal('1500.75'))
      end
      
      it 'accepts period decimal separators' do
        project.price_value = '1500.75'
        project.valid?
        expect(project.price_value).to eq(BigDecimal('1500.75'))
      end
      
      it 'validates positive numbers' do
        project.price_value = -100
        expect(project).not_to be_valid
        expect(project.errors[:price_value]).to be_present
      end
      
      it 'validates maximum value' do
        project.price_value = 99_999_999_999_999
        expect(project).not_to be_valid
        expect(project.errors[:price_value]).to be_present
      end
    end
    
    context 'commission field' do
      it 'accepts decimal values' do
        project.commission = 2.5
        expect(project).to be_valid
        expect(project.commission).to eq(2.5)
      end
      
      it 'sanitizes comma decimal separators' do
        project.commission = '2,5'
        project.valid?
        expect(project.commission).to eq(BigDecimal('2.5'))
      end
      
      it 'validates positive numbers' do
        project.commission = -1.5
        expect(project).not_to be_valid
        expect(project.errors[:commission]).to be_present
      end
      
      it 'handles empty strings by converting to nil' do
        project.commission = ''
        expect(project.commission).to be_nil
        
        project.commission = '   '
        expect(project.commission).to be_nil
      end
    end
  end
  
  describe 'Want decimal price fields' do
    let(:want) { build(:want) }
    
    context 'price_min field' do
      it 'accepts decimal values' do
        want.price_min = 1000.50
        expect(want).to be_valid
        expect(want.price_min).to eq(1000.50)
      end
      
      it 'sanitizes comma decimal separators' do
        want.price_min = '1000,50'
        want.valid?
        expect(want.price_min).to eq(BigDecimal('1000.50'))
      end
      
      it 'handles empty strings by converting to nil' do
        want.price_min = ''
        expect(want.price_min).to be_nil
      end
    end
    
    context 'price_max field' do
      it 'accepts decimal values' do
        want.price_max = 5000.99
        expect(want).to be_valid
        expect(want.price_max).to eq(5000.99)
      end
      
      it 'sanitizes comma decimal separators' do
        want.price_max = '5000,99'
        want.valid?
        expect(want.price_max).to eq(BigDecimal('5000.99'))
      end
      
      it 'handles empty strings by converting to nil' do
        want.price_max = ''
        expect(want.price_max).to be_nil
      end
    end
  end
  
  describe 'format_price helper' do
    include ApplicationHelper
    
    before do
      # Ensure Slovak locale formatting is loaded
      I18n.locale = :sk
    end
    
    it 'formats decimal values with Slovak formatting' do
      expect(format_price(1500.50)).to eq('1 500,50')
    end
    
    it 'always shows 2 decimal places for Slovak standard' do
      expect(format_price(1500.00)).to eq('1 500,00')
    end
    
    it 'handles nil values' do
      expect(format_price(nil)).to eq('–')
    end
    
    it 'formats large numbers with thousands separator' do
      expect(format_price(1_234_567.89)).to eq('1 234 567,89')
    end
    
    it 'handles zero values' do
      expect(format_price(0)).to eq('0,00')
      expect(format_price(0.00)).to eq('0,00')
    end
    
    it 'handles small decimal values' do
      expect(format_price(0.01)).to eq('0,01')
      expect(format_price(0.1)).to eq('0,10')
    end
  end
end