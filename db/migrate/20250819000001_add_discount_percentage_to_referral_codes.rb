# ABOUTME: Migration to add discount_percentage field to referral_codes table
# ABOUTME: Enables configurable discount percentages (0-100%) instead of fixed tier upgrades
class AddDiscountPercentageToReferralCodes < ActiveRecord::Migration[7.0]
  def change
    add_column :referral_codes, :discount_percentage, :decimal, precision: 5, scale: 2, null: false, default: 100.0
    
    # Add index for performance on discount percentage queries
    add_index :referral_codes, :discount_percentage
  end
end
