# ABOUTME: Migration to optimize user ordering for admin interface
# ABOUTME: Removes unused subscription_tier index and adds compound index for created_at DESC, id DESC ordering
class OptimizeUsersAdminOrdering < ActiveRecord::Migration[7.0]
  def change
    # Remove the unused subscription_tier index since admin no longer sorts by subscription tier
    remove_index :users, :subscription_tier
    
    # Add compound index for new admin ordering: created_at DESC, id DESC
    # This ensures fast pagination for admin subscriptions page
    add_index :users, [:created_at, :id], 
              order: { created_at: :desc, id: :desc }, 
              name: 'index_users_on_created_at_desc_id_desc'
  end
end