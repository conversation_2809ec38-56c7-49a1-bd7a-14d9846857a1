# ABOUTME: Migration to convert integer price fields to decimal(15,2) for Projects and Wants
# ABOUTME: Supports 2 decimal places for price_value, price_min, price_max, and commission fields

class AddDecimalSupportToPrices < ActiveRecord::Migration[7.0]
  def up
    # Convert Projects price fields to decimal
    change_column :projects, :price_value, :decimal, precision: 15, scale: 2
    
    # Convert commission from string to decimal with USING clause
    # Only convert numeric values, set non-numeric to NULL
    execute <<-SQL
      ALTER TABLE projects 
      ALTER COLUMN commission TYPE decimal(10,2) 
      USING CASE 
        WHEN commission IS NULL OR commission = '' THEN NULL
        WHEN commission ~ '^[0-9.,]+$' THEN REPLACE(commission, ',', '.')::decimal(10,2)
        ELSE NULL
      END
    SQL
    
    # Convert Wants price fields to decimal  
    change_column :wants, :price_min, :decimal, precision: 15, scale: 2
    change_column :wants, :price_max, :decimal, precision: 15, scale: 2
  end
  
  def down
    # Revert Projects price fields back to original types
    change_column :projects, :price_value, :integer
    change_column :projects, :commission, :string
    
    # Revert Wants price fields back to integer
    change_column :wants, :price_min, :integer
    change_column :wants, :price_max, :integer
  end
end
