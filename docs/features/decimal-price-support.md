# Decimal Price Support Feature

## Overview

This feature adds support for decimal prices with 2 decimal places to the Projects and Wants models, allowing Slovak-style comma decimal separators and proper number formatting.

## Implementation Details

### Database Changes

**Migration**: `20250815171642_add_decimal_support_to_prices.rb`

- Converts `price_value` and `commission` fields in Projects to `decimal(15,2)` and `decimal(10,2)` respectively
- Converts `price_min` and `price_max` fields in Wants to `decimal(15,2)`
- Safely handles existing string data in commission field with regex validation
- Preserves existing numeric data during migration

### Model Updates

**Project Model** (`app/models/project.rb`):
- Custom setters for `price_value=` and `commission=` that handle comma decimal separators
- Decimal validations with appropriate range limits
- Input sanitization before ActiveRecord type casting

**Want Model** (`app/models/want.rb`):
- Custom setters for `price_min=` and `price_max=` that handle comma decimal separators  
- Decimal validations with appropriate range limits
- Input sanitization before ActiveRecord type casting

### Number Formatting

**Locale Configuration** (`config/locales/sk.yml`):
```yaml
sk:
  number:
    format:
      separator: ","     # Decimal separator for Slovak
      delimiter: " "     # Thousands separator
      precision: 2
```

**Helper Method** (`app/helpers/application_helper.rb`):
```ruby
def format_price(value)
  return "–" if value.nil?
  return value unless value.is_a?(Numeric)
  
  number_with_precision(value, 
    precision: 2, 
    strip_insignificant_zeros: false,
    separator: I18n.t('number.format.separator'),
    delimiter: I18n.t('number.format.delimiter')
  )
end
```

### Form Updates

**Project Forms** (`app/views/projects/_form.html.erb`):
- Keep `price_value` and `commission` inputs as `text_field` for maximum flexibility
- Custom setters handle decimal conversion transparently

**Want Forms** (`app/views/wants/_form.html.erb`):
- Keep `price_min` and `price_max` inputs as `text_field` for maximum flexibility
- Custom setters handle decimal conversion transparently

### View Updates

**Project Details** (`app/views/projects/_full_details_project.html.erb`):
- Price displays now use `format_price` helper for consistent formatting

**Want Show** (`app/views/wants/show.html.erb`):
- Price range displays now use `format_price` helper for consistent formatting

## Behavior Examples

### Input Handling
- `"1500,75"` → stored as `1500.75` (comma converted to period)
- `"1500.75"` → stored as `1500.75` (period preserved)
- `1500.75` → stored as `1500.75` (numeric values preserved)
- `""` or `"   "` → stored as `nil` (empty strings converted to null)
- `nil` → stored as `nil` (null values preserved)

### Display Formatting
- `1500.50` → displayed as `"1 500,50"` (Slovak formatting with 2 decimal places)
- `1500.00` → displayed as `"1 500,00"` (always shows 2 decimal places)
- `nil` → displayed as `"–"` (safe nil handling)
- `1234567.89` → displayed as `"1 234 567,89"` (thousands separator)

## Testing

**Test Suite**: `spec/models/decimal_price_support_spec.rb`

Comprehensive tests covering:
- Decimal value assignment and validation
- Comma/period decimal separator handling
- Range validations
- Slovak number formatting
- Helper method functionality

## Key Benefits

1. **User-Friendly Input**: Supports Slovak comma decimal separators
2. **Consistent Display**: All prices formatted according to Slovak locale
3. **Data Integrity**: Proper decimal precision in database
4. **Backward Compatible**: Safely migrates existing data
5. **Rails Integration**: Uses Rails I18n for locale-aware formatting

## Migration Safety

- Existing integer price values are preserved (treated as whole euros)
- Non-numeric commission strings are converted to NULL safely
- Uses regex validation to prevent conversion errors
- Reversible migration with proper rollback handling

## Files Modified

- `db/migrate/20250815171642_add_decimal_support_to_prices.rb`
- `app/models/project.rb`
- `app/models/want.rb`
- `app/helpers/application_helper.rb`
- `app/views/projects/_form.html.erb`
- `app/views/projects/_full_details_project.html.erb`
- `app/views/wants/_form.html.erb`
- `app/views/wants/show.html.erb`
- `config/locales/sk.yml`
- `spec/models/decimal_price_support_spec.rb`