<!DOCTYPE html>
<html lang="en"><head>
<meta charset="utf-8"/>
<meta content="width=device-width, initial-scale=1.0" name="viewport"/>
<title>User Management Dashboard</title>
<link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet"/>
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&amp;display=swap" rel="stylesheet"/>
<link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet"/>
<style>
        body {
            font-family: 'Inter', sans-serif;
            background-color: #f7fafc;
        }
        .material-icons {
            font-size: 20px;
        }
        .dropdown-menu {
            display: none;
        }
        .dropdown:hover .dropdown-menu {
            display: block;
        }
        .status-badge {
            padding: 4px 12px;
            border-radius: 9999px;
            font-size: 12px;
            font-weight: 500;
            text-transform: capitalize;
        }
        .status-approved {
            background-color: #e6f7f2;
            color: #0d9488;
        }
        .status-pending {
            background-color: #fffbeb;
            color: #d97706;
        }
        .status-declined {
            background-color: #fee2e2;
            color: #dc2626;
        }
    </style>
</head>
<body class="bg-gray-50">
<div class="min-h-screen">
<div class="p-8">
<header class="flex justify-between items-center mb-8">
<div>
<h1 class="text-3xl font-bold text-gray-900">User Management</h1>
<p class="text-gray-500 mt-1">Manage user profiles, approvals, and tiers.</p>
</div>
<button class="bg-gray-800 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition">
                    Referral Codes
                </button>
</header>
<div class="bg-white p-6 rounded-2xl shadow-sm">
<div class="flex items-center space-x-2 mb-6 border-b pb-4">
<button class="bg-blue-500 text-white px-4 py-2 rounded-full text-sm font-medium">All <span class="ml-1 text-blue-200">67</span></button>
<button class="bg-gray-100 text-gray-700 px-4 py-2 rounded-full text-sm font-medium hover:bg-gray-200">Free <span class="ml-1 text-gray-400">67</span></button>
<button class="bg-gray-100 text-gray-700 px-4 py-2 rounded-full text-sm font-medium hover:bg-gray-200">Premium <span class="ml-1 text-gray-400">0</span></button>
<button class="bg-gray-100 text-gray-700 px-4 py-2 rounded-full text-sm font-medium hover:bg-gray-200">Pilot <span class="ml-1 text-gray-400">0</span></button>
<button class="bg-gray-100 text-gray-700 px-4 py-2 rounded-full text-sm font-medium hover:bg-gray-200">Approved <span class="ml-1 text-gray-400">3</span></button>
<button class="bg-gray-100 text-gray-700 px-4 py-2 rounded-full text-sm font-medium hover:bg-gray-200">Pending <span class="ml-1 text-gray-400">64</span></button>
</div>
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
<div class="bg-white border border-gray-200 rounded-xl p-5 hover:shadow-lg transition-shadow duration-300">
<div class="flex items-start justify-between">
<div class="flex items-center space-x-4">
<img alt="user avatar" class="w-12 h-12 rounded-full" src="https://lh3.googleusercontent.com/aida-public/AB6AXuAG--Td4OTFihG6WCnyoBVXynHeQzE4ICp2EimR96vIEHgprE5tMY2of4-JL7_L5qChrMQp4IyVyu4AB4YeL9K-E99lxIRnvFqdR19ccLLg5wdVpoUwBOBoGYsEY9PmouRg7h3zD1v_xIRnpBoSzw_dYbi1p_2q2dPHKKtQiHmoO2oM5G_lrG0sg2npI6Z79V8h6CX3sVh28zCDqwd1JF8rqAq_iml_otxOkEaD1xTb6YOKpG8R7wMt1QvXKBVQoP41gHgcqVX6iR4"/>
<div>
<h3 class="font-bold text-gray-900">fifth8</h3>
<p class="text-sm text-gray-500"><EMAIL></p>
</div>
</div>
<div class="status-badge status-pending">Pending</div>
</div>
<div class="mt-4">
<p class="text-sm text-gray-600">No bio</p>
<p class="text-sm text-gray-400 mt-1">No phone</p>
</div>
<div class="mt-5 flex items-center justify-between">
<div class="flex items-center space-x-2">
<span class="px-3 py-1 text-xs text-gray-600 bg-gray-100 rounded-full">Free</span>
</div>
<div class="flex items-center space-x-2">
<button class="p-2 rounded-full hover:bg-red-100 text-red-500">
<span class="material-icons">close</span>
</button>
<button class="p-2 rounded-full hover:bg-green-100 text-green-500">
<span class="material-icons">check</span>
</button>
<div class="relative dropdown">
<button class="p-2 rounded-full hover:bg-gray-200 text-gray-500">
<span class="material-icons">more_vert</span>
</button>
<div class="dropdown-menu absolute right-0 mt-2 w-40 bg-white rounded-lg shadow-xl z-10">
<a class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100" href="#">Edit User</a>
<a class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100" href="#">Change Tier</a>
<a class="block px-4 py-2 text-sm text-red-600 hover:bg-red-50" href="#">Delete User</a>
</div>
</div>
</div>
</div>
</div>
<div class="bg-white border border-gray-200 rounded-xl p-5 hover:shadow-lg transition-shadow duration-300">
<div class="flex items-start justify-between">
<div class="flex items-center space-x-4">
<img alt="user avatar" class="w-12 h-12 rounded-full" src="https://lh3.googleusercontent.com/aida-public/AB6AXuDztzcn7_0KSvse5epAhWTG4J3mXm6CJ1vO3ja-E_1747P11NbNLFHOEaYoxbpTD2qKV0EpQkqdIbOUPIdkEUqILLoge8QGZ1Km22ZytzA5B1n2BhIfCqQOaM4bB-oIRmMGJjJVUiusbzb9pLtA0m52wbYqpCSNj5Itmoy0_h-M_juwcc6yKDJYHPcwGBL0XRvKf7m0pSWus5XIyOBCObFtkUxv7x96a02ACHlFmtQHn1gXc7hVUSGERClvfcEHuLAbuIIYYv1HhSQ"/>
<div>
<h3 class="font-bold text-gray-900">user0</h3>
<p class="text-sm text-gray-500"><EMAIL></p>
</div>
</div>
<div class="status-badge status-approved">Approved</div>
</div>
<div class="mt-4">
<p class="text-sm text-gray-600">No bio</p>
<p class="text-sm text-gray-400 mt-1">No phone</p>
</div>
<div class="mt-5 flex items-center justify-between">
<div class="flex items-center space-x-2">
<span class="px-3 py-1 text-xs text-gray-600 bg-gray-100 rounded-full">Free</span>
</div>
<div class="flex items-center space-x-2">
<button class="p-2 rounded-full hover:bg-red-100 text-red-500">
<span class="material-icons">close</span>
</button>
<button class="p-2 rounded-full hover:bg-green-100 text-green-500">
<span class="material-icons">check</span>
</button>
<div class="relative dropdown">
<button class="p-2 rounded-full hover:bg-gray-200 text-gray-500">
<span class="material-icons">more_vert</span>
</button>
<div class="dropdown-menu absolute right-0 mt-2 w-40 bg-white rounded-lg shadow-xl z-10">
<a class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100" href="#">Edit User</a>
<a class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100" href="#">Change Tier</a>
<a class="block px-4 py-2 text-sm text-red-600 hover:bg-red-50" href="#">Delete User</a>
</div>
</div>
</div>
</div>
</div>
<div class="bg-white border border-gray-200 rounded-xl p-5 hover:shadow-lg transition-shadow duration-300">
<div class="flex items-start justify-between">
<div class="flex items-center space-x-4">
<img alt="user avatar" class="w-12 h-12 rounded-full" src="https://lh3.googleusercontent.com/aida-public/AB6AXuCPxqT-q_IZj0fR6eHFi-rBdc3GRbm70s-PfC7Aivjh7e4hf33d6tH7Dx_45hUYUbWD0jQhCLY6z8N4Fu6aI9AUERyBqwsB4bKHWctliK2zzNcmxPWuGVUNWFG1L2aDIXKDskxhmsy0v8z8AuwFxfbMLJR_Kj_QgL9HTUyPiGw03mCm_mBODl6JHk3HimsCbf70iBX97KNRaFy4acipIpZ88q2zMlTAtxrpbgI6sD98cg-8xLKgiB4YDF9DhvPiGEgFQ-6nJJOaUdw"/>
<div>
<h3 class="font-bold text-gray-900">tester0</h3>
<p class="text-sm text-gray-500"><EMAIL></p>
</div>
</div>
<div class="status-badge status-pending">Pending</div>
</div>
<div class="mt-4">
<p class="text-sm text-gray-600">No bio</p>
<p class="text-sm text-gray-400 mt-1">No phone</p>
</div>
<div class="mt-5 flex items-center justify-between">
<div class="flex items-center space-x-2">
<span class="px-3 py-1 text-xs text-gray-600 bg-gray-100 rounded-full">Free</span>
</div>
<div class="flex items-center space-x-2">
<button class="p-2 rounded-full hover:bg-red-100 text-red-500">
<span class="material-icons">close</span>
</button>
<button class="p-2 rounded-full hover:bg-green-100 text-green-500">
<span class="material-icons">check</span>
</button>
<div class="relative dropdown">
<button class="p-2 rounded-full hover:bg-gray-200 text-gray-500">
<span class="material-icons">more_vert</span>
</button>
<div class="dropdown-menu absolute right-0 mt-2 w-40 bg-white rounded-lg shadow-xl z-10">
<a class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100" href="#">Edit User</a>
<a class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100" href="#">Change Tier</a>
<a class="block px-4 py-2 text-sm text-red-600 hover:bg-red-50" href="#">Delete User</a>
</div>
</div>
</div>
</div>
</div>
<div class="bg-white border border-gray-200 rounded-xl p-5 hover:shadow-lg transition-shadow duration-300">
<div class="flex items-start justify-between">
<div class="flex items-center space-x-4">
<img alt="user avatar" class="w-12 h-12 rounded-full" src="https://lh3.googleusercontent.com/aida-public/AB6AXuDwrm-uzEbCQaFph-lF41dhZzfgnd2VGEXnM_AE4gEVZGvU0fO4Hm6G0KUWphd04GyHz5U6zscFDfHqgE-fmR60_ESMyZNxcVn3Zj2lFNLRu3etGdR_BXn0ag00v1Zw7XU6info_JzPfCe0MQrwomReKD8iRbaCPaqGwzRbiFA0K5JxVqFamp1GEWrxQYb4-DffBU1qWyc853-6eaiGidjmG_bo3qE6y2TUdE70sRmkH_HJQU2r2PTAH2EdMONG8ypEwBkfLK8r0pM"/>
<div>
<h3 class="font-bold text-gray-900">third0</h3>
<p class="text-sm text-gray-500"><EMAIL></p>
</div>
</div>
<div class="status-badge status-pending">Pending</div>
</div>
<div class="mt-4">
<p class="text-sm text-gray-600">No bio</p>
<p class="text-sm text-gray-400 mt-1">No phone</p>
</div>
<div class="mt-5 flex items-center justify-between">
<div class="flex items-center space-x-2">
<span class="px-3 py-1 text-xs text-gray-600 bg-gray-100 rounded-full">Free</span>
</div>
<div class="flex items-center space-x-2">
<button class="p-2 rounded-full hover:bg-red-100 text-red-500">
<span class="material-icons">close</span>
</button>
<button class="p-2 rounded-full hover:bg-green-100 text-green-500">
<span class="material-icons">check</span>
</button>
<div class="relative dropdown">
<button class="p-2 rounded-full hover:bg-gray-200 text-gray-500">
<span class="material-icons">more_vert</span>
</button>
<div class="dropdown-menu absolute right-0 mt-2 w-40 bg-white rounded-lg shadow-xl z-10">
<a class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100" href="#">Edit User</a>
<a class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100" href="#">Change Tier</a>
<a class="block px-4 py-2 text-sm text-red-600 hover:bg-red-50" href="#">Delete User</a>
</div>
</div>
</div>
</div>
</div>
<div class="bg-white border border-gray-200 rounded-xl p-5 hover:shadow-lg transition-shadow duration-300">
<div class="flex items-start justify-between">
<div class="flex items-center space-x-4">
<img alt="user avatar" class="w-12 h-12 rounded-full" src="https://lh3.googleusercontent.com/aida-public/AB6AXuA-UldXFhjZQjwdkb5M-6hRCd9Z9HMTdUwD4O7W9XVmaym0o8iYBFrGz2B3KSTDkOmIGDfW68JFIk3gh_lTnUPPXFN6xV_u9e_kzFko-tO9KbfCS4ViJjwWplVSpxslM1LPOkoKbTkpjBRMEZNHwYgyaww2OjIMqmGe6qrzcbo6YArFqwvzaNXaeIz2AEyp0qWW7wobmVLvN_B5VH9sB0IM69F6t5Bq_7l0A-Zl9nqcSy9Z8Q4aBaaxNjSGAm0SiiQmDYnliFOgTNc"/>
<div>
<h3 class="font-bold text-gray-900">third1</h3>
<p class="text-sm text-gray-500"><EMAIL></p>
</div>
</div>
<div class="status-badge status-pending">Pending</div>
</div>
<div class="mt-4">
<p class="text-sm text-gray-600">No bio</p>
<p class="text-sm text-gray-400 mt-1">No phone</p>
</div>
<div class="mt-5 flex items-center justify-between">
<div class="flex items-center space-x-2">
<span class="px-3 py-1 text-xs text-gray-600 bg-gray-100 rounded-full">Free</span>
</div>
<div class="flex items-center space-x-2">
<button class="p-2 rounded-full hover:bg-red-100 text-red-500">
<span class="material-icons">close</span>
</button>
<button class="p-2 rounded-full hover:bg-green-100 text-green-500">
<span class="material-icons">check</span>
</button>
<div class="relative dropdown">
<button class="p-2 rounded-full hover:bg-gray-200 text-gray-500">
<span class="material-icons">more_vert</span>
</button>
<div class="dropdown-menu absolute right-0 mt-2 w-40 bg-white rounded-lg shadow-xl z-10">
<a class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100" href="#">Edit User</a>
<a class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100" href="#">Change Tier</a>
<a class="block px-4 py-2 text-sm text-red-600 hover:bg-red-50" href="#">Delete User</a>
</div>
</div>
</div>
</div>
</div>
</div>
</div>
</div>
</div>

</body></html>