---
description: Use Codex to run tests, auto-fix, and re-run until green; return a patch and summary for review
category: code-analysis-testing
allowed-tools: Bash, Read, Edit, LS
---

Run a test/fix loop with <PERSON>, then review and integrate the patch.

## Process

1. Detect project tests
   - JavaScript/TypeScript: read `package.json` for `check`, `test`, `lint`, `validate`, `verify`.
   - Python: prefer `pytest` (fallback to `unittest`), optionally `flake8`/`mypy`.
   - Go: `go test ./...` (optionally `golangci-lint run`).
   - Rust: `cargo test` or `cargo check`.
   - Ruby: `rake test` or `bundle exec rspec`.

2. Invoke Codex to iterate
   - Run tests and capture failures.
   - Apply minimal patches to fix failing tests only.
   - Re-run until all pass or iteration/budget is exhausted.

3. Save artifacts to `./.codex-artifacts/`
   - `patch.diff` — final unified diff for all changes.
   - `test.log` — full test output across iterations.
   - `summary.md` — concise explanation of what changed and why.

4. Human/Claude review
   - <PERSON> reviews `summary.md`, inspects `patch.diff`, and applies changes selectively.
   - Provide risks, follow-ups, and notes for maintainers.

## Shell

Preferred (Codex CLI installed as `codex`):

```
mkdir -p ./.codex-artifacts
codex testfix \
  --max-iterations 5 \
  --save ./.codex-artifacts \
  --respect-ignore true
```

Fallback (no Codex CLI):

```
bash scripts/codex_testfix.sh
```

The fallback script should implement roughly the same loop (run tests → minimally patch → retry) and write the same artifacts.

## Constraints

- Do not commit automatically; ask for human approval after review.
- Keep changes minimal and focused on failing tests.
- Preserve public APIs unless explicitly allowed to change.

## Notes

- For monorepos (Nx/Bazel/etc.), detect and scope tests per package to reduce runtime.
- Standardize artifact paths to make downstream analysis easier for Claude.

