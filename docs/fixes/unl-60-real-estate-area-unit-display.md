# UNL-60: Fix Real Estate Area & Unit Fields Display

## Issue Description

**Problem**: When creating a real estate project and filling in the Area (Plocha) and Unit (Jednotka) fields, these fields were not displayed in the project preview/show page.

**Original Slovak Issue**: Ak pri vytváraní ponuky zvolím kategóriu <PERSON>, zobrazí mi dodatočné input fields Plocha a Jednotka špecifické pre túto kategóriu. Vyplním ich, ale pri prezeraní ponuky v náhľadovom režime mi polia Plocha a Jednotka neukáže.

## Root Cause

The real estate area and unit fields (`land_area` and `area_unit`) were being saved to the database correctly through the form but were missing from the project display template. The `app/views/projects/_full_details_project.html.erb` template did not include conditional rendering for these real estate-specific fields.

## Solution

### Changes Made

1. **Template Enhancement**: Added conditional display logic to `app/views/projects/_full_details_project.html.erb`
2. **Test Coverage**: Created comprehensive feature tests to verify the functionality

### Implementation Details

#### View Template Changes

Added to `app/views/projects/_full_details_project.html.erb` after the subcategory field:

```erb
<% if project.real_estate? && (project.land_area.present? || project.area_unit.present?) %>
  <div class="detail-item">
    <div class="detail-label"><%= t('models.project.attributes.land_area', default: 'Plocha') %></div>
    <div class="detail-value">
      <% if project.land_area.present? %>
        <%= [project.land_area, project.area_unit].reject(&:blank?).join(' ') %>
      <% else %>
        <%= t('common.not_specified', default: 'Nešpecifikované') %>
      <% end %>
    </div>
  </div>
<% end %>
```

#### Logic Explanation

- **Condition**: Only shows for real estate projects (`project.real_estate?`) that have either area or unit data
- **Display Format**: Uses `reject(&:blank?).join(' ')` to properly space area value and unit (e.g., "1500 m²")
- **Empty State**: Shows "Nešpecifikované" (Not specified) when area is missing but unit exists
- **Code Quality**: Cleaner implementation that handles edge cases and maintains proper spacing
- **Localization**: Uses existing translation keys for consistent Slovak/English support

### Test Coverage

Created `spec/features/real_estate_fields_display_spec.rb` with three scenarios:

1. **Display Test**: Verifies area and unit are shown for real estate projects with data
2. **Non-Real Estate Test**: Confirms area fields are NOT shown for other project types  
3. **Empty Values Test**: Ensures graceful handling when no area/unit data exists

## Database Schema

The fix utilizes existing database fields:
- `land_area` (integer) - The area value
- `area_unit` (string) - The unit description (e.g., "m²", "ha")

## Form Integration

No changes needed to the form - the fields were already correctly implemented in:
- `app/views/projects/_form.html.erb` contains the real estate fields section
- Form conditionally shows/hides based on category selection via JavaScript

## Localization

Uses existing translation keys:
- `models.project.attributes.land_area`: "Plocha" (SK), "Area" (EN)
- `models.project.attributes.area_unit`: "Jednotka" (SK), "Unit" (EN)

## Testing

All tests pass:
```bash
bundle exec rspec spec/features/real_estate_fields_display_spec.rb
```

## Categories Affected

This fix applies to all real estate project types:
- Commercial Property (commercial_property)
- Land (land) 
- Homes (homes)
- Offices (offices)

## Impact

- **User Experience**: Real estate area and unit information now properly displays
- **Data Integrity**: No data loss - existing records will show correctly
- **Backward Compatibility**: Projects without area data are unaffected
- **Performance**: Minimal impact - simple conditional rendering

## Follow-up Considerations

1. **Localization**: Currently displays in application's default language - consider implementing proper Slovak locale support for the project detail pages
2. **Field Validation**: Consider adding front-end validation for area unit formats
3. **Historical Data**: Existing real estate projects with area data will automatically benefit from this fix

## Related Files

- `app/views/projects/_full_details_project.html.erb` - Main fix
- `spec/features/real_estate_fields_display_spec.rb` - Test coverage
- `app/models/project.rb` - Contains enum definitions and business logic
- `config/locales/sk.yml` and `config/locales/en.yml` - Translation keys