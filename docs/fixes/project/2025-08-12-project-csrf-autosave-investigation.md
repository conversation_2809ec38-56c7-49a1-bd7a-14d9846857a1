# Project CSRF/Autosave Investigation

Date: 2025-08-12
Owner: Augment Agent

## Summary
- After creating a draft project (POST /projects), subsequent autosave PATCH /projects/:id succeeded.
- A regular HTML form submission to PATCH /projects/:id then failed with ActionController::InvalidAuthenticityToken.
- The failing request included `upload_files` param (from the visible file input).

Root cause: The form’s action is changed from POST /projects to PATCH /projects/:id by JavaScript after the first autosave creates the record, but the per-form authenticity token is not refreshed. With per-form CSRF tokens enabled (Rails 7 default), the original token becomes invalid for the new action/method, causing the HTML submit to be rejected.

## Relevant Log Excerpt
```
12:57:10 web.1  | Started PATCH "/projects/163" for ************ at 2025-08-12 12:57:12 +0200
12:57:12 web.1  | Processing by ProjectsController#update as HTML
12:57:12 web.1  |   Parameters: { ..., "project"=>"[FILTERED]", "upload_files"=>[""], "id"=>"163"}
12:57:12 web.1  | Can't verify CSRF token authenticity.
12:57:12 web.1  | Completed 422 Unprocessable Entity ...
12:57:12 web.1  | ActionController::InvalidAuthenticityToken (Can't verify CSRF token authenticity.)
```

## Evidence in Code

### 1) JavaScript changes form action but not authenticity token
File: app/javascript/autosave.js
```js
// After first autosave-created project:
this.form.action = `/projects/${this.projectId}`;
// Add hidden _method=patch so the form becomes PATCH
if (!this.form.querySelector('input[name="_method"]')) {
  const methodInput = document.createElement('input');
  methodInput.type = 'hidden';
  methodInput.name = '_method';
  methodInput.value = 'patch';
  this.form.appendChild(methodInput);
}
// NOTE: authenticity_token input is not refreshed here
```

### 2) Rails is enforcing CSRF for HTML submits
File: app/controllers/application_controller.rb
```ruby
class ApplicationController < ActionController::Base
  protect_from_forgery with: :exception
end
```

### 3) The form is initially rendered for new or existing records
File: app/views/projects/_form.html.erb
```erb
<%= form_with(model: project, id: "project_form", html: { class: 'large-form', data: { project_id: project.persisted? ? project.id : nil } }) do |form| %>
```
- For new records, form_with targets POST /projects.
- After autosave creates the record, JS switches action to PATCH /projects/:id.

### 4) Meta CSRF token exists for AJAX, but does not refresh form token automatically
File: app/views/layouts/application.html.erb
```erb
<%= csrf_meta_tags %>
```
Autosave fetches and uploads use this meta token; however, form authenticity_token inputs are separate and (by default) scoped per form action.

## Why it fails only after autosave
- Initial form token is valid for POST /projects.
- Autosave creates the Project and rewires the form to PATCH /projects/:id without refreshing authenticity_token.
- Next HTML submit uses a token bound to the old action/method, and Rails rejects it as invalid.

## Secondary Observations (not the proximate cause)
- “Unpermitted parameters: :project_status, :summary_only, :full_access, :network_only, :semi_public” logs appear during autosave requests. This is expected because `autosave_params` intentionally excludes these fields.
- JS uses optional chaining when reading the meta CSRF token; if the tag were missing, fetches would send an undefined token. Not the cause here, but worth hardening.

## Recommended Fix Options (choose one)

1) Redirect to edit after first autosave creation (simplest)
- After the first autosave creates the project (server returns project_id), programmatically navigate to `/projects/:id/edit`.
- Rails will render the form with the correct action/method and a fresh authenticity token.
- Minimal code; robust against token mismatches.

2) Refresh authenticity_token in-place after rewriting action (SPA-style)
- Provide a tiny endpoint that returns `form_authenticity_token` for a given action/method (or a standard token if per-form scoping is off).
- After autosave sets `this.projectId` and rewrites `form.action`, fetch a fresh token and replace the hidden `authenticity_token` input in the form before any user submit.
- Keep the user on the same page without redirect.

3) Disable per-form CSRF tokens globally (NOT recommended)
- Reduces protection; not advised.

## Implementation Pointers (when ready)

If implementing Option 1 (redirect):
- In `autosave.js`, upon receiving `project_id` in the first POST response, set `window.location = `/${currentLocale}/projects/${projectId}/edit`` (respecting locale if needed).

If implementing Option 2 (refresh token):
- Add a controller action like `CSRFController#show` that renders `{ token: form_authenticity_token }` as JSON.
- Or more strictly, generate a token for the exact path/method if per-form scoping is enforced and you want path-accurate tokens.
- In `autosave.js`, after setting `this.projectId` and `this.form.action`, fetch the token and ensure the form contains:
  - `<input type="hidden" name="authenticity_token" value="...">`
  - `<input type="hidden" name="_method" value="patch">`

## Files to Touch Later (no changes yet)
- app/javascript/autosave.js (add redirect or token refresh logic)
- Optionally: new small controller and route for token JSON (if refreshing without redirect)

## Open Questions
- Are we okay with a short redirect to `/projects/:id/edit` after the first autosave? It’s the least error-prone.
- If staying SPA-style, do we prefer a simple /csrf_token JSON endpoint, or re-rendering a partial to replace the whole form?

## Appendix: Other related bits
- Autosave requests set headers from the meta token:
  - app/javascript/autosave.js uses `'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').getAttribute('content')`
  - app/frontend/js/upload_handler.js sets the same header for `/uploads`.
- Strong parameters:
  - `project_params` includes file arrays and sensitive fields (for full saves).
  - `autosave_params` excludes sensitive publication/visibility fields; intended to show as "Unpermitted" in logs when sent during autosave.

