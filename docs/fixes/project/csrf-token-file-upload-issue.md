# CSRF Token Issue with Project File Upload

## Issue Summary
ActionController::InvalidAuthenticityToken error occurs during project form submission when file uploads are attempted, even after successful autosave operations.

## Possible Core Issues Discovered

### 1. **Session/CSRF Token Synchronization Issue**
- **Evidence**: Same user/project works via AJAX (`*/*`) but fails via HTML form submission
- **Theory**: CSRF token becomes stale/invalid between autosave cycles and manual form submission
- **Why intermittent**: Token refresh timing varies

### 2. **Form Submission Method Inconsistency**
- **Evidence**: 
  - Successful: `Processing by ProjectsController#update as */*` (AJAX)
  - Failed: `Processing by ProjectsController#update as HTML` (form submission)
- **Theory**: Some mechanism triggers HTML form submission instead of AJAX autosave
- **Why intermittent**: User behavior or timing differences

### 3. **Empty File Field Contamination**
- **Evidence**: `"upload_files"=>[""]` appears in failed requests even without files
- **Theory**: `file_field_tag 'upload_files[]'` inside Rails form always submits, even when empty
- **Why intermittent**: Only affects manual form submission, not AJAX autosave

### 4. **Autosave vs Manual Submit Race Condition**
- **Evidence**: Autosave code only prevents form submission when `pendingChanges = true`
- **Theory**: Manual form submission happens when autosave thinks no changes are pending
- **Code**: `e.preventDefault()` only called if `this.pendingChanges && !this.isSaving`
- **Why intermittent**: Timing-dependent on user actions

### 5. **Deferred Project Creation Side Effects**
- **Evidence**: Issue introduced in commit `67f5b98` (July 30) implementing deferred creation
- **Theory**: Changes to form handling/autosave behavior introduced CSRF token handling bugs
- **Why intermittent**: New vs existing project behavior differs

### 6. **JavaScript Autosave Manager State Issues**
- **Evidence**: Form has autosave functionality that should handle most interactions
- **Theory**: Autosave manager fails to intercept certain form submissions
- **Why intermittent**: Depends on JavaScript execution state/timing

### Most Likely Culprit:
**#4 - Autosave vs Manual Submit Race Condition** combined with **#3 - Empty File Field Contamination**

The autosave clears its `pendingChanges` flag, then user manually submits form containing empty `upload_files[]`, which triggers HTML processing instead of AJAX, causing CSRF failure.

## User Reproduction Flow

### Detailed User Journey:
1. **User starts new project** - Creates form with deferred project creation
2. **Fills fields bottom to top** - User enters data field by field
3. **Autosave works** - AJAX requests successfully create/update project:
   ```
   Processing by ProjectsController#create as */*   ✅
   Processing by ProjectsController#update as */*   ✅
   ```
4. **User reaches file upload** - Attempts to select/upload file
5. **Gets file upload error** - System shows "You have to create a project before uploading files"
6. **User clicks Update button** - Despite project being created via autosave, button still says "Update"
7. **CSRF error occurs** - Form submits as HTML instead of AJAX:
   ```
   Processing by ProjectsController#update as HTML  ❌
   Parameters: {"upload_files"=>["filename.png"], ...}
   ActionController::InvalidAuthenticityToken
   ```

### Key Log Evidence:
```
13:11:55 - Processing by ProjectsController#create as */*     ✅ (autosave creates project)
13:12:07 - Processing by ProjectsController#update as */*     ✅ (autosave updates work)
13:12:46 - Processing by ProjectsController#update as HTML    ❌ (manual submit fails)
           Parameters: {"upload_files"=>["2025-08-11...png"]}
           ActionController::InvalidAuthenticityToken
```

## Critical Analysis

### The File Upload Error Message Contradiction:
- Autosave successfully creates project (ID 165 created at 13:11:55)
- User still gets "create project before uploading" error
- This suggests upload handler doesn't recognize the project as existing
- User forced to manually submit form, triggering CSRF error

### Form State Inconsistency:
- Project exists (created via autosave)
- Upload handler thinks project doesn't exist
- Form button still shows "Update" rather than reflecting true state
- Manual form submission includes `upload_files[]` parameter

### Technical Root Cause Theory:
The **deferred project creation** (commit 67f5b98) introduced a timing issue where:
1. Autosave creates project successfully
2. Upload handler's `targetId` validation fails to recognize existing project
3. User forced to manually submit form
4. Manual submission goes through HTML form processing (not AJAX)
5. HTML form processing can't handle CSRF token properly in this context

### Late Change Hypothesis:
The issue likely stems from the deferred creation changes where:
- Upload handler checks `if (!targetId)` but doesn't refresh after autosave creates project
- File input remains part of form submission instead of being handled separately
- CSRF token handling differs between AJAX autosave and HTML form submission paths

## Status: Investigation Ongoing
- Root cause appears to be interaction between deferred project creation and file upload validation
- Upload handler state not synchronized with autosave project creation
- Manual form submission pathway contaminated with file upload parameters
- CSRF token validation failing specifically on HTML form submission path