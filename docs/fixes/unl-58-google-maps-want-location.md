# UNL-58: Fix Google Maps API Not Working in Want/Dopyt Location Field

## Issue Description

Google Maps autocomplete was not functioning in Want/Dopyt creation forms, while it worked correctly in Project creation forms. Users couldn't use Google Maps location suggestions when adding a Want/Demand.

## Root Cause

The Google Maps initialization code in `app/frontend/entrypoints/application.js` only included field IDs for `['location', 'project_location']` but missing the Want form's location field ID `'want_place'`.

## Solution

### Files Modified
- `app/frontend/entrypoints/application.js` - Added `'want_place'` to locationInputs array

### Code Changes
```javascript
// Before:
const locationInputs = ['location', 'project_location'];

// After:
const locationInputs = ['location', 'project_location', 'want_place'];
```

### Field Mapping
- **Project form**: Uses `id: 'project_location'` for location field
- **Want form**: Uses `id: 'want_place'` for location field  
- **Search filters**: Uses `id: 'location'` for location field

## Technical Details

The Google Maps Places API autocomplete initialization:
1. Checks if any of the specified field IDs exist on the page
2. Loads Google Maps API lazily when user focuses on location field
3. Initializes autocomplete with places suggestions
4. Handles location data storage in hidden country fields

## Testing

### Manual Testing Steps
1. Navigate to Want creation form (`/wants/new`)
2. Focus on the "Place" field
3. Start typing a location (e.g., "Bratisl")
4. Verify Google Maps autocomplete suggestions appear
5. Select a location and verify it's populated correctly

### Expected Behavior
- Google Maps autocomplete should work identically to Project location field
- Location suggestions should appear when typing
- Selected location should populate the place field and hidden country fields

## Files Referenced
- `app/views/wants/_form.html.erb` - Want form with place field
- `app/views/projects/_form.html.erb` - Project form with location field  
- `app/frontend/entrypoints/application.js` - Google Maps initialization

## Verification
The fix ensures consistent location input behavior across all forms in the application, providing users with the same Google Maps autocomplete functionality whether creating Projects or Wants.