# Threat Model - Unlisters Application

**Last Updated**: January 11, 2025  
**Security Review**: Passed (Gemini AI Audit)  

## 1. Architecture Overview

### System Components
```
┌─────────────┐     HTTPS      ┌──────────────┐     ┌────────────┐
│   Browser   │ ───────────────▶│  Rails API   │────▶│ PostgreSQL │
│ (Vue.js 3)  │                 │  (Puma)      │     └────────────┘
└─────────────┘                 └──────────────┘            │
       │                                │                    │
       │                                ▼                    ▼
       │                         ┌──────────────┐     ┌────────────┐
       └── JWT Tokens ──────────▶│ Rack::Attack │     │   Redis    │
                                 │ Rate Limiter │     │  (Cache)   │
                                 └──────────────┘     └────────────┘
                                        │
                                        ▼
                                 ┌──────────────┐
                                 │   AWS S3     │
                                 │ (File Store) │
                                 └──────────────┘
```

### Trust Boundaries
- **Browser → Rails API**: All user input enters here (forms, file uploads, API calls)
- **Rails → Database**: Trusted after input validation
- **Rails → S3**: File operations through Active Storage
- **JWT Validation**: Token verification at API boundary

## 2. Authentication & Session Management (JWT Focus)

### JWT Strategy
- **Algorithm**: `HS256` (HMAC-SHA256)
  - **Rationale**: Simpler secret management for monolithic architecture
- **Secret Management**: 
  - Stored in `Rails.application.secret_key_base`
  - Configured via Rails credentials (encrypted)
  - Rotation requires deployment (invalidates all tokens)
- **Token Payload**:
  ```json
  {
    "user_id": 123,
    "file_id": 456,
    "project_id": 789,
    "exp": 1704067200,
    "iat": 1704066900,
    "nonce": "a1b2c3d4e5f6"
  }
  ```
- **Token Lifetime**: 5 minutes for file access tokens
  - **Rationale**: Balances security with user experience
- **Token Storage**: Not stored - generated on demand
- **Revocation**: Short-lived tokens as primary mechanism
  - No blocklist needed due to 5-minute expiry

### Session Management
- **Devise**: Cookie-based sessions for web interface
- **CSRF Protection**: Rails `protect_from_forgery`
- **Secure Cookies**: `HttpOnly`, `Secure` flags in production

## 3. Authorization Model

### Framework
- **ActionPolicy**: Policy-based authorization
- **Pundit-style**: Resource-based policies

### Key Policies
```ruby
# Project visibility matrix
network_only + summary_only = Basic project info
network_only + full_details = Full project + files
semi_public + summary_only = Public listing
semi_public + full_details = Complete access
```

### File Access Authorization
1. User requests file token via authenticated API
2. `ProjectPolicy` validates user can `:view_full_details?`
3. JWT token generated with user/project/file scope
4. Token validates authorization on every stream request

## 4. Key Security Controls & Patterns

### Input Validation
```ruby
# Strong Parameters pattern
def project_params
  params.require(:project).permit(:title, :summary, :location, 
    :category, :subcategory, :price, :currency)
end
```

### Cross-Site Scripting (XSS) Prevention
- **ERB Auto-escaping**: Default Rails protection
- **Content Security Policy**:
  ```
  Content-Security-Policy: default-src 'none'; 
    style-src 'unsafe-inline'; sandbox;
  ```
- **`raw`/`html_safe` Policy**: Prohibited without security review

### Cross-Site Request Forgery (CSRF)
- **Rails Protection**: `protect_from_forgery with: :exception`
- **API Integration**: CSRF token in meta tag for AJAX
- **JWT API**: Stateless endpoints exempt (Bearer auth only)

### Secure File Handling
```
Upload Flow:
1. User → Rails (multipart form)
2. Rails → Active Storage → S3
3. Generate HMAC-SHA256 hash for file identification

Access Flow:
1. User requests token (authenticated)
2. Authorization check via ActionPolicy
3. Generate 5-minute JWT
4. Stream file with security headers
```

**Security Headers for Files**:
- `X-Content-Type-Options: nosniff`
- `X-Frame-Options: DENY`
- `Content-Security-Policy: default-src 'none'`
- `X-XSS-Protection: 1; mode=block`

### Rate Limiting (Rack::Attack)
| Endpoint | Limit | Period | Scope |
|----------|-------|--------|-------|
| Login attempts | 5 | 1 minute | IP |
| File token requests | 30 | 1 minute | IP |
| File token requests | 50 | 1 minute | User |
| File streaming | 60 | 1 minute | IP |

### Dependencies Security
- **CI/CD Pipeline**: 
  - `brakeman` - Static security analysis
  - `bundler-audit` - Gem vulnerability scanning
  - RSpec security test suite

## 5. Known Risks & Mitigations

| Threat | Past Vulnerability | Current Mitigation |
|--------|-------------------|-------------------|
| Authentication Bypass | Legacy streaming endpoint lacked auth checks | All file access requires authentication; legacy endpoint fixed |
| Unreliable Security Controls | HTTP Referer header validation | Removed; uses cryptographic JWT tokens only |
| Authorization Ambiguity | Generic authorization checks | Explicit policy methods (`:manage_access?`) |
| XSS via File Content | Missing CSP headers | Comprehensive security headers on all file responses |
| DoS via File Search | O(n) hash lookup | 100-file limit with performance monitoring |
| Session Hijacking | Test methods in production | Environment-specific method availability |
| IDOR | Direct file ID access | HMAC-SHA256 hashes with project scoping |

## 6. Security Testing

### Automated Testing
```bash
# Security test suite
bundle exec rspec spec/requests/*security*

# Static analysis
bundle exec brakeman

# Dependency audit
bundle audit check
```

### Key Test Categories
1. **IDOR Protection**: Cross-user file access prevention
2. **JWT Security**: Token tampering detection
3. **Hash Security**: Enumeration prevention
4. **DoS Protection**: Resource exhaustion limits
5. **XSS Prevention**: CSP validation

## 7. Future Enhancements

### Planned Improvements
1. **Token Revocation**: Redis-backed blocklist for emergency revocation
2. **File Hash Indexing**: Database column for O(1) lookups
3. **Content Validation**: Magic byte verification for uploads
4. **Single-Use Tokens**: Prevent token replay attacks

### Architecture Evolution
- Consider RS256 for JWT (public key verification)
- Implement API rate limiting by API key
- Add Web Application Firewall (WAF) rules

## 8. Recent Security Enhancements (December 2025)

### Full Access Authorization System Implementation
- **Enhanced Authorization**: Project authorization now properly honors user-configured sharing preferences
- **Approval Gate**: Added mandatory approval checking (`approved?`) for all non-owner access  
- **Guest Protection**: Unauthenticated users are properly redirected to sign-in pages
- **Nil Safety**: Graceful handling of nil user inputs prevents crashes and security bypasses
- **Comprehensive Testing**: Full test coverage for authorization edge cases and security scenarios

#### Security Controls Added
1. **Unapproved Project Protection**: Projects with `approved: false` are inaccessible to non-owners
2. **Guest Access Prevention**: All project access requires authentication
3. **Input Validation**: Nil user parameters handled gracefully without crashes
4. **Test Coverage**: Security scenarios validated across policy, model, request, and integration layers

#### Files Modified for Security
- `app/policies/project_policy.rb`: Enhanced with approval and guest checking
- `app/models/project.rb`: Added nil safety and approval validation
- `app/views/projects/_all_projects.html.erb`: Secured UI logic
- Comprehensive test suite covering all security scenarios

## 9. CVE Assessment & Vulnerability Management (August 2025)

### CVE-2025-24293: Active Storage Transformation Vulnerability

#### Vulnerability Details
- **CVE ID**: CVE-2025-24293
- **Severity**: High (CVSS 8.2)
- **Component**: Active Storage image transformation methods
- **Attack Vector**: Command injection via user-controlled variant parameters

#### Application Risk Assessment: **MINIMAL**

**Protected Factors:**
1. **No User Input to Variants**: All `.variant()` calls use hardcoded parameters (`resize_to_limit: [300, 200]`)
2. **Disabled Rails Processing**: `ThumbnailGenerationJob` has variant generation disabled (line 48-49)
3. **Lambda Architecture**: All file transformations handled by external Lambda microservice
4. **Zero Vulnerable Patterns**: No instances of `params` passed to variant methods found in codebase
5. **Webhook-Only Communication**: Rails receives pre-processed thumbnails via HMAC-signed webhook

#### Architecture Analysis
```
┌─────────────┐    File Upload    ┌──────────────┐    S3 Storage    ┌────────────┐
│   Browser   │ ─────────────────▶│  Rails API   │ ───────────────▶│   AWS S3   │
└─────────────┘                   └──────────────┘                  └────────────┘
                                         │                                   │
                                         │                                   │
                                  Webhook │                                   │ S3 Event
                                         ▼                                   ▼
                                  ┌──────────────┐    Processing     ┌────────────┐
                                  │ Webhook API  │◀─────────────────│   Lambda   │
                                  │ (HMAC Auth)  │                  │ (ImageMagick)│
                                  └──────────────┘                  └────────────┘
```

**Key Security Properties:**
- **Isolation**: Image processing isolated to Lambda service
- **No Direct User Input**: Rails never processes user-supplied transformation parameters
- **Secure Communication**: Webhook uses HMAC-SHA256 authentication
- **Pre-processed Content**: Rails only handles final thumbnail blobs

#### Current Status
- **Rails Version**: ******* (technically affected)
- **Actual Risk**: **LOW** - Architecture prevents exploitation
- **Action Required**: Upgrade to Rails *******+ during next maintenance window
- **Priority**: Non-urgent due to architectural protection

#### Verification Steps Taken
1. ✅ Searched codebase for user-controlled variant parameters: **None found**
2. ✅ Analyzed `ThumbnailGenerationJob`: **All variant processing disabled**
3. ✅ Reviewed file processing pipeline: **Lambda-only architecture confirmed**
4. ✅ Checked webhook security: **HMAC authentication verified**
5. ✅ Validated no vulnerable patterns: **Zero instances of `params` in variant calls**

**Assessment Date**: August 15, 2025  
**Assessor**: Claude Code Security Analysis  
**Review Status**: Complete - Low priority upgrade recommended

---

**Next Security Review**: July 2026 or after major changes