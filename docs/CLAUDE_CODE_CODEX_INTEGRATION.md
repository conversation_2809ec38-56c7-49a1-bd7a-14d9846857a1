# Claude Code + Codex CLI: Cooperation Guide

This guide explains practical ways to make Claude Code and Codex CLI work together effectively, plus a ready‑to‑use command you can run from Claude Code to delegate certain tasks to <PERSON>.

## Short Answer

- There is no built‑in, direct bridge between Claude Code and Codex.
- They can still cooperate well via:
  - Claude Code slash commands that shell out to Codex (or other CLIs).
  - Shared tools/servers (e.g., MCP) and a common project workspace.
  - Orchestrators that run multiple coding agents side‑by‑side.

## Why Use Both

- **Alternative viewpoint:** Have Codex propose independent fixes/refactors; <PERSON> reviews and integrates. Diverse outputs surface better solutions.
- **Background executor:** Let Codex run long test/fix loops while <PERSON> remains interactive for oversight and review.
- **Red‑team testing:** Codex can fuzz, scan, or chaos‑test; <PERSON> reads reports and patches issues.
- **Large‑scale codemods:** Offload repo‑wide AST codemods to Codex; <PERSON> plans, validates, and cleans up follow‑ups.
- **Multi‑variant generation:** Generate multiple solution variants with Codex; <PERSON> selects and integrates the best one.
- **Structured automation:** Use Codex for deterministic jobs (bisects, dependency pinning, flaky test isolation). <PERSON> explains and documents changes.

## Patterns That Work

- **<PERSON> → Codex via Shell:** Claude Code commands can run Bash, so they can call `codex ...` directly. Capture artifacts (diffs, logs, summaries) to a known folder for <PERSON> to inspect.
- **Shared workspace & artifacts:** Keep both agents operating on the same repo; write outputs to e.g. `./.codex-artifacts/` for easy analysis and review.
- **Cross‑review:** Treat patches from one agent as input for the other; this catches mistakes early and maintains quality.
- **Parallelization:** Use an orchestrator (e.g., `claude-squad`) to run both assistants in parallel sessions/branches.

## Orchestration Option

- **Claude Squad (`smtg-ai/claude-squad`):** Terminal app to manage multiple agents (Claude Code, Codex, others) in isolated `tmux` sessions and git worktrees. Launch each assistant with its own program and branch to avoid conflicts, review diffs, and keep tasks organized.

## A Useful Command: Delegate Test‑Fix Loops to Codex

Add the command file in `docs/agents/codex-testfix.md` (copy to your Claude Code commands dir when ready). It:
- Detects how to run tests for the repo.
- Calls Codex to run tests → apply minimal patches → retry until pass or budget exhausted.
- Stores artifacts (patch diff, test log, summary) under `./.codex-artifacts/`.
- Returns artifacts to Claude for analysis and selective apply.

See `docs/agents/codex-testfix.md` in this repo for the exact command content.

## Caveats

- Direct, in‑process invocation (one assistant as a tool inside the other) isn’t publicly supported.
- Headless triggering of Claude Code commands is not officially documented; run them from Claude Code or through an orchestrator.
- Keep auto‑commit off; require human or Claude review before applying patches.

## Quick Setup Tips

- Standardize artifact paths (e.g., `./.codex-artifacts/`) so Claude can reliably read results.
- If using a monorepo (Nx/Bazel/etc.), scope tests to the changed package(s) to speed up loops.
- Consider adding additional commands: `codex-codemod`, `codex-redteam`, `codex-bisect` following the same pattern.

